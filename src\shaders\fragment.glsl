#version 450

// Input from vertex shader
layout(location = 0) in vec3 fragWorldPos;
layout(location = 1) in vec3 fragNormal;
layout(location = 2) in vec2 fragTexCoord;
layout(location = 3) in vec3 fragCameraPos;
layout(location = 4) in vec3 fragLightPos;
layout(location = 5) in vec3 fragLightColor;
layout(location = 6) in float fragLightIntensity;

// Output color
layout(location = 0) out vec4 outColor;

// Simple Blinn-Phong lighting
vec3 calculateLighting(vec3 worldPos, vec3 normal, vec3 cameraPos, vec3 lightPos, vec3 lightColor, float lightIntensity) {
    // Normalize normal
    vec3 N = normalize(normal);
    
    // Light direction
    vec3 L = normalize(lightPos - worldPos);
    
    // View direction
    vec3 V = normalize(cameraPos - worldPos);
    
    // Half vector for Blinn-Phong
    vec3 H = normalize(L + V);
    
    // Distance attenuation
    float distance = length(lightPos - worldPos);
    float attenuation = 1.0 / (1.0 + 0.09 * distance + 0.032 * distance * distance);
    
    // Ambient component
    vec3 ambient = 0.1 * lightColor;
    
    // Diffuse component
    float NdotL = max(dot(N, L), 0.0);
    vec3 diffuse = NdotL * lightColor * lightIntensity * attenuation;
    
    // Specular component (Blinn-Phong)
    float NdotH = max(dot(N, H), 0.0);
    float specular = pow(NdotH, 32.0);
    vec3 specularColor = specular * lightColor * lightIntensity * attenuation;
    
    return ambient + diffuse + specularColor;
}

void main() {
    // Base material color (simple gray for now)
    vec3 baseColor = vec3(0.7, 0.7, 0.7);
    
    // Calculate lighting
    vec3 lighting = calculateLighting(fragWorldPos, fragNormal, fragCameraPos, 
                                    fragLightPos, fragLightColor, fragLightIntensity);
    
    // Final color
    vec3 finalColor = baseColor * lighting;
    
    // Gamma correction
    finalColor = pow(finalColor, vec3(1.0/2.2));
    
    outColor = vec4(finalColor, 1.0);
}
