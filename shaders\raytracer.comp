#version 450

layout(local_size_x = 8, local_size_y = 8, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform writeonly image2D outputImage;

layout(binding = 1) uniform CameraUBO {
    vec3 position;
    vec3 direction;
    vec3 up;
    vec3 right;
    float fov;
    float aspect_ratio;
    int width;
    int height;
} camera;

struct Ray {
    vec3 origin;
    vec3 direction;
};

// Simple sphere intersection for testing
bool intersectSphere(Ray ray, vec3 center, float radius, out float t) {
    vec3 oc = ray.origin - center;
    float a = dot(ray.direction, ray.direction);
    float b = 2.0 * dot(oc, ray.direction);
    float c = dot(oc, oc) - radius * radius;
    float discriminant = b * b - 4.0 * a * c;

    if (discriminant < 0.0) {
        return false;
    }

    float sqrt_discriminant = sqrt(discriminant);
    float t1 = (-b - sqrt_discriminant) / (2.0 * a);
    float t2 = (-b + sqrt_discriminant) / (2.0 * a);

    t = (t1 > 0.0) ? t1 : t2;
    return t > 0.0;
}

vec3 castRay(Ray ray) {
    float t;

    // Test sphere at origin
    if (intersectSphere(ray, vec3(0.0, 0.0, -5.0), 1.0, t)) {
        vec3 hit_point = ray.origin + ray.direction * t;
        vec3 normal = normalize(hit_point - vec3(0.0, 0.0, -5.0));

        // Simple lighting
        vec3 light_dir = normalize(vec3(1.0, 1.0, 1.0));
        float ndotl = max(0.0, dot(normal, light_dir));

        return vec3(0.8, 0.3, 0.3) * (0.2 + 0.8 * ndotl);
    }

    // Sky gradient
    float t_sky = 0.5 * (ray.direction.y + 1.0);
    return mix(vec3(1.0, 1.0, 1.0), vec3(0.5, 0.7, 1.0), t_sky);
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);

    // Use camera dimensions if valid, otherwise use image size
    int width = (camera.width > 0) ? camera.width : int(imageSize(outputImage).x);
    int height = (camera.height > 0) ? camera.height : int(imageSize(outputImage).y);

    if (coord.x >= width || coord.y >= height) {
        return;
    }

    // Use camera data if valid, otherwise use defaults
    vec3 camera_pos = camera.position;
    vec3 camera_dir = normalize(camera.direction);
    vec3 camera_up = normalize(camera.up);
    vec3 camera_right = normalize(camera.right);
    float fov = camera.fov;
    float aspect = camera.aspect_ratio;

    // Validate and fix camera data
    if (length(camera_dir) < 0.1) camera_dir = vec3(0.0, 0.0, -1.0);
    if (length(camera_up) < 0.1) camera_up = vec3(0.0, 1.0, 0.0);
    if (length(camera_right) < 0.1) camera_right = vec3(1.0, 0.0, 0.0);
    if (fov <= 0.0 || fov > 180.0) fov = 60.0; // FOV in degrees
    if (aspect <= 0.0) aspect = float(width) / float(height);

    // Calculate ray direction
    float x = (float(coord.x) + 0.5) / float(width);
    float y = (float(coord.y) + 0.5) / float(height);

    // Convert to NDC [-1, 1]
    x = x * 2.0 - 1.0;
    y = 1.0 - y * 2.0; // Flip Y

    // Convert FOV to radians and calculate view plane dimensions
    float fov_rad = fov * 3.14159 / 180.0;
    float half_height = tan(fov_rad * 0.5);
    float half_width = half_height * aspect;

    // Calculate ray direction in world space
    vec3 ray_dir = normalize(
        camera_dir +
        camera_right * (x * half_width) +
        camera_up * (y * half_height)
    );

    Ray ray;
    ray.origin = camera_pos;
    ray.direction = ray_dir;

    vec3 color = castRay(ray);

    imageStore(outputImage, coord, vec4(color, 1.0));
}
