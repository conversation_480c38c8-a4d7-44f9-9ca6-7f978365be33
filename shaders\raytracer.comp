#version 450

layout(local_size_x = 8, local_size_y = 8, local_size_z = 1) in;

layout(binding = 0, rgba8) uniform writeonly image2D outputImage;

layout(binding = 1) uniform CameraUBO {
    vec3 position;
    vec3 direction;
    vec3 up;
    vec3 right;
    float fov;
    float aspect_ratio;
    int width;
    int height;
} camera;

struct Ray {
    vec3 origin;
    vec3 direction;
};

// Simple sphere intersection for testing
bool intersectSphere(Ray ray, vec3 center, float radius, out float t) {
    vec3 oc = ray.origin - center;
    float a = dot(ray.direction, ray.direction);
    float b = 2.0 * dot(oc, ray.direction);
    float c = dot(oc, oc) - radius * radius;
    float discriminant = b * b - 4.0 * a * c;

    if (discriminant < 0.0) {
        return false;
    }

    float sqrt_discriminant = sqrt(discriminant);
    float t1 = (-b - sqrt_discriminant) / (2.0 * a);
    float t2 = (-b + sqrt_discriminant) / (2.0 * a);

    t = (t1 > 0.0) ? t1 : t2;
    return t > 0.0;
}

vec3 castRay(Ray ray) {
    float t;

    // Test sphere at origin
    if (intersectSphere(ray, vec3(0.0, 0.0, -5.0), 1.0, t)) {
        vec3 hit_point = ray.origin + ray.direction * t;
        vec3 normal = normalize(hit_point - vec3(0.0, 0.0, -5.0));

        // Simple lighting
        vec3 light_dir = normalize(vec3(1.0, 1.0, 1.0));
        float ndotl = max(0.0, dot(normal, light_dir));

        return vec3(0.8, 0.3, 0.3) * (0.2 + 0.8 * ndotl);
    }

    // Sky gradient
    float t_sky = 0.5 * (ray.direction.y + 1.0);
    return mix(vec3(1.0, 1.0, 1.0), vec3(0.5, 0.7, 1.0), t_sky);
}

void main() {
    ivec2 coord = ivec2(gl_GlobalInvocationID.xy);
    ivec2 size = imageSize(outputImage);

    if (coord.x >= size.x || coord.y >= size.y) {
        return;
    }

    // Use camera from uniform buffer

    // Calculate ray direction
    float x = (float(coord.x) + 0.5) / float(size.x);
    float y = (float(coord.y) + 0.5) / float(size.y);

    // Convert to NDC [-1, 1]
    x = x * 2.0 - 1.0;
    y = 1.0 - y * 2.0; // Flip Y

    // Calculate view plane dimensions
    float half_height = tan(camera.fov * 0.5);
    float half_width = half_height * camera.aspect_ratio;

    // Calculate ray direction in world space
    vec3 ray_dir = normalize(
        camera.direction +
        camera.right * (x * half_width) +
        camera.up * (y * half_height)
    );

    Ray ray;
    ray.origin = camera.position;
    ray.direction = ray_dir;

    vec3 color = castRay(ray);

    imageStore(outputImage, coord, vec4(color, 1.0));
}
