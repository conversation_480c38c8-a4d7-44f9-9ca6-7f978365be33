{
    find_program_mingw_arch_x64_plat_windows_checktoolcxx = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program_mingw_arch_x64_plat_windows_checktoolld = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--enable-long-section-names"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--default-image-base-low"] = true,
            ["--stats"] = true,
            ["--enable-auto-import"] = true,
            ["--disable-long-section-names"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--add-stdcall-alias"] = true,
            ["-rpath-link"] = true,
            ["-dp"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--orphan-handling"] = true,
            ["-Ttext"] = true,
            ["--print-map"] = true,
            ["--gpsize"] = true,
            ["--default-image-base-high"] = true,
            ["--gc-keep-exported"] = true,
            ["--disable-auto-image-base"] = true,
            ["--warn-common"] = true,
            ["--cref"] = true,
            ["--no-keep-memory"] = true,
            ["-qmagic"] = true,
            ["--minor-subsystem-version"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--print-map-locals"] = true,
            ["--error-handling-script"] = true,
            ["--stack"] = true,
            ["--exclude-symbols"] = true,
            ["--demangle"] = true,
            ["--discard-none"] = true,
            ["--trace-symbol"] = true,
            ["-c"] = true,
            ["--task-link"] = true,
            ["--just-symbols"] = true,
            ["--dynamic-list"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["-no-pie"] = true,
            ["--warn-duplicate-exports"] = true,
            ["--sort-common"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--discard-locals"] = true,
            ["--warn-alternate-em"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--no-warnings"] = true,
            ["--dynamic-linker"] = true,
            ["--warn-multiple-gp"] = true,
            ["--heap"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["-R"] = true,
            ["--support-old-code"] = true,
            ["--section-alignment"] = true,
            ["--omagic"] = true,
            ["-b"] = true,
            ["-Trodata-segment"] = true,
            ["--no-print-map-locals"] = true,
            ["--architecture"] = true,
            ["--no-export-dynamic"] = true,
            ["-g"] = true,
            ["--nmagic"] = true,
            ["--print-sysroot"] = true,
            ["--pop-state"] = true,
            ["-y"] = true,
            ["--dependency-file"] = true,
            ["--push-state"] = true,
            ["-L"] = true,
            ["--no-dynamic-linker"] = true,
            ["--script"] = true,
            ["-I"] = true,
            ["-Tbss"] = true,
            ["--export-all-symbols"] = true,
            ["--no-as-needed"] = true,
            ["--library-path"] = true,
            ["--exclude-all-symbols"] = true,
            ["-init"] = true,
            ["--subsystem"] = true,
            ["--enable-reloc-section"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["-Y"] = true,
            ["-EL"] = true,
            ["-a"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--large-address-aware"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-print-map-discarded"] = true,
            ["--remap-inputs-file"] = true,
            ["-l"] = true,
            ["--force-exe-suffix"] = true,
            ["-A"] = true,
            ["--gc-sections"] = true,
            ["--print-map-discarded"] = true,
            ["--no-strip-discarded"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--image-base"] = true,
            ["--library"] = true,
            ["--major-os-version"] = true,
            ["--check-sections"] = true,
            ["--pic-executable"] = true,
            ["-rpath"] = true,
            ["--version"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["-Tldata-segment"] = true,
            ["--verbose"] = true,
            ["--dll"] = true,
            ["--enable-auto-image-base"] = true,
            ["--undefined"] = true,
            ["--output"] = true,
            ["--default-script"] = true,
            ["-dT"] = true,
            ["-h"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["-Ur"] = true,
            ["--disable-large-address-aware"] = true,
            ["--no-print-gc-sections"] = true,
            ["-debug"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-warn-mismatch"] = true,
            ["--major-image-version"] = true,
            ["--filter"] = true,
            ["--out-implib"] = true,
            ["--end-group"] = true,
            ["-flto"] = true,
            ["--undefined-version"] = true,
            ["--sort-section"] = true,
            ["-o"] = true,
            ["--no-relax"] = true,
            ["--warn-section-align"] = true,
            ["--no-fatal-warnings"] = true,
            ["--strip-debug"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--target-help"] = true,
            ["-F"] = true,
            ["--require-defined"] = true,
            ["--relocatable"] = true,
            ["-nostdlib"] = true,
            ["-fini"] = true,
            ["-Qy"] = true,
            ["--relax"] = true,
            ["--enable-linker-version"] = true,
            ["--minor-image-version"] = true,
            ["--dynamic-list-data"] = true,
            ["--no-define-common"] = true,
            ["--major-subsystem-version"] = true,
            ["--export-dynamic"] = true,
            ["--section-start"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["-assert"] = true,
            ["--defsym"] = true,
            ["-m"] = true,
            ["-Tdata"] = true,
            ["--ctf-variables"] = true,
            ["--format"] = true,
            ["--discard-all"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--remap-inputs"] = true,
            ["--default-imported-symver"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--file-alignment"] = true,
            ["-static"] = true,
            ["-T"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--warn-textrel"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--no-ctf-variables"] = true,
            ["-O"] = true,
            ["--no-undefined-version"] = true,
            ["-G"] = true,
            ["--print-output-format"] = true,
            ["--fatal-warnings"] = true,
            ["--traditional-format"] = true,
            ["--mri-script"] = true,
            ["-f"] = true,
            ["-Bshareable"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--section-ordering-file"] = true,
            ["-V"] = true,
            ["--compat-implib"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--allow-multiple-definition"] = true,
            ["-Bsymbolic"] = true,
            ["-plugin-opt"] = true,
            ["--output-def"] = true,
            ["--emit-relocs"] = true,
            ["--print-gc-sections"] = true,
            ["-u"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--disable-linker-version"] = true,
            ["--warn-once"] = true,
            ["--strip-all"] = true,
            ["--no-undefined"] = true,
            ["--oformat"] = true,
            ["--entry"] = true,
            ["--print-memory-usage"] = true,
            ["-Map"] = true,
            ["--auxiliary"] = true,
            ["--split-by-file"] = true,
            ["-EB"] = true,
            ["--retain-symbols-file"] = true,
            ["--trace"] = true,
            ["--force-group-allocation"] = true,
            ["--minor-os-version"] = true,
            ["--map-whole-files"] = true,
            ["-Bno-symbolic"] = true,
            ["--help"] = true,
            ["-e"] = true,
            ["--exclude-libs"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--strip-discarded"] = true,
            ["--no-map-whole-files"] = true,
            ["--default-symver"] = true,
            ["--no-check-sections"] = true,
            ["--disable-reloc-section"] = true,
            ["--version-exports-section"] = true,
            ["--split-by-reloc"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--unique"] = true,
            ["--wrap"] = true,
            ["--no-omagic"] = true,
            ["--disable-auto-import"] = true,
            ["--no-demangle"] = true,
            ["--version-script"] = true,
            ["-Ttext-segment"] = true,
            ["--as-needed"] = true,
            ["--no-gc-sections"] = true,
            ["--whole-archive"] = true,
            ["--start-group"] = true,
            ["-soname"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-plugin"] = true,
            ["--kill-at"] = true,
            ["-plugin-save-temps"] = true
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--version"] = true,
            ["-shared"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-pie"] = true,
            ["-dumpversion"] = true,
            ["-print-multi-lib"] = true,
            ["-S"] = true,
            ["--param"] = true,
            ["-o"] = true,
            ["-time"] = true,
            ["-c"] = true,
            ["-pipe"] = true,
            ["-print-multi-directory"] = true,
            ["-Xpreprocessor"] = true,
            ["--target-help"] = true,
            ["-print-multiarch"] = true,
            ["-v"] = true,
            ["-B"] = true,
            ["-x"] = true,
            ["-print-multi-os-directory"] = true,
            ["-print-search-dirs"] = true,
            ["-save-temps"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-Xlinker"] = true,
            ["-Xassembler"] = true,
            ["-pass-exit-codes"] = true,
            ["-dumpmachine"] = true,
            ["-no-canonical-prefixes"] = true,
            ["--help"] = true,
            ["-E"] = true,
            ["-dumpspecs"] = true,
            ["-print-sysroot"] = true
        }
    },
    ["detect.sdks.find_mingw"] = {
        mingw = {
            sdkdir = [[C:\msys64\mingw64]],
            cross = "x86_64-w64-mingw32-",
            bindir = [[C:\msys64\mingw64\bin]]
        }
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcc = {
        ["x86_64-w64-mingw32-gcc"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]]
    },
    find_programver = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = "15.1.0"
    },
    ["lib.detect.has_flags"] = {
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdata-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-march=native"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-fdata-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-s"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-MMD -MF"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-march=native"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-Wno-gnu-line-marker -Werror"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-Os"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdiagnostics-color=always"] = true
    },
    find_program_mingw_arch_x64_plat_windows_checktoolsh = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    }
}