{
    find_program_mingw_arch_x64_plat_windows_checktoolsh = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program = {
        git = [[C:\Users\<USER>\AppData\Local\.xmake\packages\g\git\2.20.0\65b10f9e7885425fbf2d2abaad2a61a9\share\MinGit\cmd\git.exe]],
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        ping = "ping"
    },
    ["lib.detect.has_flags"] = {
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-fdata-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-MMD -MF"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-march=native"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-Wno-gnu-line-marker -Werror"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-s"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-Os"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdiagnostics-color=always"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdata-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-march=native"] = true
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--export-dynamic-symbol-list"] = true,
            ["-R"] = true,
            ["-y"] = true,
            ["--no-map-whole-files"] = true,
            ["--oformat"] = true,
            ["--default-image-base-high"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--split-by-file"] = true,
            ["--defsym"] = true,
            ["--disable-large-address-aware"] = true,
            ["--architecture"] = true,
            ["--reduce-memory-overheads"] = true,
            ["-F"] = true,
            ["--cref"] = true,
            ["-b"] = true,
            ["-plugin"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--no-warn-mismatch"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["-Ur"] = true,
            ["--no-whole-archive"] = true,
            ["--start-group"] = true,
            ["--map-whole-files"] = true,
            ["-Trodata-segment"] = true,
            ["--output"] = true,
            ["-fini"] = true,
            ["--force-group-allocation"] = true,
            ["-EB"] = true,
            ["-soname"] = true,
            ["--traditional-format"] = true,
            ["--relax"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["-Tbss"] = true,
            ["--end-group"] = true,
            ["--exclude-symbols"] = true,
            ["--trace-symbol"] = true,
            ["--stats"] = true,
            ["--disable-auto-image-base"] = true,
            ["--support-old-code"] = true,
            ["--emit-relocs"] = true,
            ["--large-address-aware"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--strip-debug"] = true,
            ["--no-keep-memory"] = true,
            ["--enable-linker-version"] = true,
            ["--allow-multiple-definition"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--exclude-all-symbols"] = true,
            ["--help"] = true,
            ["--script"] = true,
            ["-assert"] = true,
            ["--export-all-symbols"] = true,
            ["--minor-image-version"] = true,
            ["--undefined-version"] = true,
            ["--default-imported-symver"] = true,
            ["--no-demangle"] = true,
            ["--kill-at"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--no-fatal-warnings"] = true,
            ["--default-symver"] = true,
            ["-plugin-save-temps"] = true,
            ["-EL"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--minor-os-version"] = true,
            ["--export-dynamic"] = true,
            ["--print-output-format"] = true,
            ["--unique"] = true,
            ["--disable-long-section-names"] = true,
            ["--enable-long-section-names"] = true,
            ["--print-gc-sections"] = true,
            ["-h"] = true,
            ["--force-exe-suffix"] = true,
            ["--omagic"] = true,
            ["-Ttext-segment"] = true,
            ["--warn-duplicate-exports"] = true,
            ["--no-print-map-locals"] = true,
            ["--error-handling-script"] = true,
            ["--push-state"] = true,
            ["--no-ctf-variables"] = true,
            ["--no-as-needed"] = true,
            ["--version-script"] = true,
            ["--orphan-handling"] = true,
            ["--no-relax"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["-e"] = true,
            ["--default-script"] = true,
            ["--gpsize"] = true,
            ["--warn-common"] = true,
            ["--sort-section"] = true,
            ["-Tldata-segment"] = true,
            ["--entry"] = true,
            ["--minor-subsystem-version"] = true,
            ["--discard-locals"] = true,
            ["-o"] = true,
            ["-dp"] = true,
            ["--whole-archive"] = true,
            ["--dll"] = true,
            ["-flto"] = true,
            ["--trace"] = true,
            ["--gc-sections"] = true,
            ["--retain-symbols-file"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--out-implib"] = true,
            ["-V"] = true,
            ["--disable-auto-import"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--wrap"] = true,
            ["-qmagic"] = true,
            ["--warn-section-align"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--just-symbols"] = true,
            ["-u"] = true,
            ["--no-export-dynamic"] = true,
            ["-Ttext"] = true,
            ["--print-map-locals"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--dependency-file"] = true,
            ["--no-omagic"] = true,
            ["--demangle"] = true,
            ["--warn-alternate-em"] = true,
            ["--mri-script"] = true,
            ["--dynamic-list"] = true,
            ["--remap-inputs-file"] = true,
            ["-Map"] = true,
            ["--file-alignment"] = true,
            ["--sort-common"] = true,
            ["--warn-once"] = true,
            ["-static"] = true,
            ["--nmagic"] = true,
            ["-f"] = true,
            ["--enable-auto-image-base"] = true,
            ["--default-image-base-low"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--format"] = true,
            ["--subsystem"] = true,
            ["--add-stdcall-alias"] = true,
            ["--output-def"] = true,
            ["--require-defined"] = true,
            ["--verbose"] = true,
            ["--image-base"] = true,
            ["--discard-all"] = true,
            ["--major-image-version"] = true,
            ["--no-define-common"] = true,
            ["--warn-multiple-gp"] = true,
            ["--exclude-libs"] = true,
            ["--ctf-variables"] = true,
            ["--stack"] = true,
            ["--dynamic-linker"] = true,
            ["-l"] = true,
            ["--task-link"] = true,
            ["--print-map"] = true,
            ["--warn-textrel"] = true,
            ["--no-gc-sections"] = true,
            ["-rpath"] = true,
            ["--no-print-gc-sections"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["-m"] = true,
            ["-Tdata"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--enable-reloc-section"] = true,
            ["--discard-none"] = true,
            ["-nostdlib"] = true,
            ["-O"] = true,
            ["--undefined"] = true,
            ["-plugin-opt"] = true,
            ["-dT"] = true,
            ["-T"] = true,
            ["--remap-inputs"] = true,
            ["-G"] = true,
            ["--major-subsystem-version"] = true,
            ["--enable-auto-import"] = true,
            ["-Bshareable"] = true,
            ["-g"] = true,
            ["--no-check-sections"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["-Qy"] = true,
            ["--library"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--heap"] = true,
            ["--pic-executable"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--filter"] = true,
            ["--gc-keep-exported"] = true,
            ["--dynamic-list-data"] = true,
            ["--no-undefined-version"] = true,
            ["-no-pie"] = true,
            ["--target-help"] = true,
            ["--compat-implib"] = true,
            ["-Bno-symbolic"] = true,
            ["--major-os-version"] = true,
            ["-L"] = true,
            ["--print-map-discarded"] = true,
            ["-rpath-link"] = true,
            ["--relocatable"] = true,
            ["--no-print-map-discarded"] = true,
            ["--no-strip-discarded"] = true,
            ["-Bsymbolic"] = true,
            ["--library-path"] = true,
            ["--version-exports-section"] = true,
            ["--as-needed"] = true,
            ["-Y"] = true,
            ["--strip-discarded"] = true,
            ["--pop-state"] = true,
            ["--strip-all"] = true,
            ["--no-warnings"] = true,
            ["--fatal-warnings"] = true,
            ["--section-start"] = true,
            ["--print-memory-usage"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--section-ordering-file"] = true,
            ["-debug"] = true,
            ["--no-undefined"] = true,
            ["--auxiliary"] = true,
            ["--version"] = true,
            ["--disable-reloc-section"] = true,
            ["-c"] = true,
            ["--check-sections"] = true,
            ["-init"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["-I"] = true,
            ["--no-dynamic-linker"] = true,
            ["-a"] = true,
            ["--split-by-reloc"] = true,
            ["--print-sysroot"] = true,
            ["-A"] = true,
            ["--disable-linker-version"] = true,
            ["--section-alignment"] = true
        }
    },
    find_program_mingw_arch_x64_plat_windows_checktoolld = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["-pass-exit-codes"] = true,
            ["-dumpversion"] = true,
            ["--target-help"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-dumpspecs"] = true,
            ["-time"] = true,
            ["-print-multi-directory"] = true,
            ["-shared"] = true,
            ["-v"] = true,
            ["-print-multiarch"] = true,
            ["--version"] = true,
            ["-print-search-dirs"] = true,
            ["--param"] = true,
            ["-S"] = true,
            ["-print-multi-os-directory"] = true,
            ["-B"] = true,
            ["-dumpmachine"] = true,
            ["-o"] = true,
            ["-Xpreprocessor"] = true,
            ["-pie"] = true,
            ["-pipe"] = true,
            ["-E"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-c"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-Xlinker"] = true,
            ["-x"] = true,
            ["--help"] = true,
            ["-save-temps"] = true,
            ["-print-multi-lib"] = true,
            ["-Xassembler"] = true,
            ["-print-sysroot"] = true
        }
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcxx = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcc = {
        ["x86_64-w64-mingw32-gcc"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]]
    },
    ["detect.sdks.find_mingw"] = {
        mingw = {
            cross = "x86_64-w64-mingw32-",
            sdkdir = [[C:\msys64\mingw64]],
            bindir = [[C:\msys64\mingw64\bin]]
        }
    },
    find_programver = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = "15.1.0"
    }
}