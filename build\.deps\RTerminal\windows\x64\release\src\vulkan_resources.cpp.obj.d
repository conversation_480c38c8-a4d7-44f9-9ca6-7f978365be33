{
    depfiles_format = "gcc",
    files = {
        [[src\vulkan_resources.cpp]]
    },
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-Os",
            "-std=c++17",
            [[-IE:\VulkanSDK\Include]],
            "-fexceptions",
            "-O3",
            "-ffast-math",
            "-ftree-vectorize",
            "-funroll-loops",
            "-fomit-frame-pointer",
            "-ffunction-sections",
            "-fdata-sections",
            "-march=native"
        }
    },
    depfiles = "vulkan_resources.o: src\\vulkan_resources.cpp src\\vulkan_backend.h  src\\renderer.h src\\console.h src\\vector.h src\\constants.h src\\scene.h  src\\triangle.h src\\object.h src\\ray.h src\\aabb.h src\\material.h  src\\texture.h src\\ui.h src\\light.h src\\bvh.h src\\console_utils.h  E:\\VulkanSDK\\Include/vulkan/vulkan.h  E:\\VulkanSDK\\Include/vulkan/vk_platform.h  E:\\VulkanSDK\\Include/vulkan/vulkan_core.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codecs_common.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std_encode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std_encode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std_decode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std_decode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std_decode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std_encode.h  E:\\VulkanSDK\\Include/vulkan/vulkan_win32.h\
"
}