{
    files = {
        [[src\vulkan_resources.cpp]]
    },
    depfiles = "vulkan_resources.o: src\\vulkan_resources.cpp src\\vulkan_backend.h  src\\renderer.h src\\console.h src\\vector.h src\\constants.h src\\scene.h  src\\triangle.h src\\object.h src\\ray.h src\\aabb.h src\\material.h  src\\texture.h src\\ui.h src\\light.h src\\bvh.h src\\console_utils.h  E:\\VulkanSDK\\Include/vulkan/vulkan.h  E:\\VulkanSDK\\Include/vulkan/vk_platform.h  E:\\VulkanSDK\\Include/vulkan/vulkan_core.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codecs_common.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std_encode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std_encode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h264std_decode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_h265std_decode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std_decode.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std.h  E:\\VulkanSDK\\Include/vk_video/vulkan_video_codec_av1std_encode.h  E:\\VulkanSDK\\Include/vulkan/vulkan_win32.h  E:\\VulkanSDK\\Include/glm/glm.hpp  E:\\VulkanSDK\\Include/glm/detail/_fixes.hpp  E:\\VulkanSDK\\Include/glm/detail/setup.hpp  E:/VulkanSDK/Include/glm/simd/platform.h  E:\\VulkanSDK\\Include/glm/fwd.hpp  E:\\VulkanSDK\\Include/glm/detail/qualifier.hpp  E:\\VulkanSDK\\Include/glm/detail/setup.hpp  E:\\VulkanSDK\\Include/glm/vec2.hpp  E:/VulkanSDK/Include/glm/ext/vector_bool2.hpp  E:/VulkanSDK/Include/glm/detail/type_vec2.hpp  E:/VulkanSDK/Include/glm/detail/type_vec2.inl  E:/VulkanSDK/Include/glm/detail/compute_vector_relational.hpp  E:/VulkanSDK/Include/glm/detail/setup.hpp  E:/VulkanSDK/Include/glm/ext/vector_bool2_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_float2.hpp  E:/VulkanSDK/Include/glm/ext/vector_float2_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_double2.hpp  E:/VulkanSDK/Include/glm/ext/vector_double2_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_int2.hpp  E:/VulkanSDK/Include/glm/ext/vector_int2_sized.hpp  E:/VulkanSDK/Include/glm/ext/scalar_int_sized.hpp  E:/VulkanSDK/Include/glm/detail/setup.hpp  E:/VulkanSDK/Include/glm/ext/vector_uint2.hpp  E:/VulkanSDK/Include/glm/ext/vector_uint2_sized.hpp  E:/VulkanSDK/Include/glm/ext/scalar_uint_sized.hpp  E:\\VulkanSDK\\Include/glm/vec3.hpp  E:/VulkanSDK/Include/glm/ext/vector_bool3.hpp  E:/VulkanSDK/Include/glm/detail/type_vec3.hpp  E:/VulkanSDK/Include/glm/detail/type_vec3.inl  E:/VulkanSDK/Include/glm/ext/vector_bool3_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_float3.hpp  E:/VulkanSDK/Include/glm/ext/vector_float3_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_double3.hpp  E:/VulkanSDK/Include/glm/ext/vector_double3_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_int3.hpp  E:/VulkanSDK/Include/glm/ext/vector_int3_sized.hpp  E:/VulkanSDK/Include/glm/ext/vector_uint3.hpp  E:/VulkanSDK/Include/glm/ext/vector_uint3_sized.hpp  E:\\VulkanSDK\\Include/glm/vec4.hpp  E:/VulkanSDK/Include/glm/ext/vector_bool4.hpp  E:/VulkanSDK/Include/glm/detail/type_vec4.hpp  E:/VulkanSDK/Include/glm/detail/type_vec4.inl  E:/VulkanSDK/Include/glm/ext/vector_bool4_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_float4.hpp  E:/VulkanSDK/Include/glm/ext/vector_float4_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_double4.hpp  E:/VulkanSDK/Include/glm/ext/vector_double4_precision.hpp  E:/VulkanSDK/Include/glm/ext/vector_int4.hpp  E:/VulkanSDK/Include/glm/ext/vector_int4_sized.hpp  E:/VulkanSDK/Include/glm/ext/vector_uint4.hpp  E:/VulkanSDK/Include/glm/ext/vector_uint4_sized.hpp  E:\\VulkanSDK\\Include/glm/mat2x2.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double2x2.hpp  E:/VulkanSDK/Include/glm/detail/type_mat2x2.hpp  E:/VulkanSDK/Include/glm/detail/type_mat2x2.inl  E:/VulkanSDK/Include/glm/matrix.hpp  E:/VulkanSDK/Include/glm/detail/setup.hpp  E:/VulkanSDK/Include/glm/mat2x3.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double2x3.hpp  E:/VulkanSDK/Include/glm/detail/type_mat2x3.hpp  E:/VulkanSDK/Include/glm/detail/type_mat2x3.inl  E:/VulkanSDK/Include/glm/ext/matrix_double2x3_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float2x3.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float2x3_precision.hpp  E:/VulkanSDK/Include/glm/mat2x4.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double2x4.hpp  E:/VulkanSDK/Include/glm/detail/type_mat2x4.hpp  E:/VulkanSDK/Include/glm/detail/type_mat2x4.inl  E:/VulkanSDK/Include/glm/ext/matrix_double2x4_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float2x4.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float2x4_precision.hpp  E:/VulkanSDK/Include/glm/mat3x2.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double3x2.hpp  E:/VulkanSDK/Include/glm/detail/type_mat3x2.hpp  E:/VulkanSDK/Include/glm/detail/type_mat3x2.inl  E:/VulkanSDK/Include/glm/ext/matrix_double3x2_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float3x2.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float3x2_precision.hpp  E:/VulkanSDK/Include/glm/mat3x3.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double3x3.hpp  E:/VulkanSDK/Include/glm/detail/type_mat3x3.hpp  E:/VulkanSDK/Include/glm/detail/type_mat3x3.inl  E:/VulkanSDK/Include/glm/ext/matrix_double3x3_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float3x3.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float3x3_precision.hpp  E:/VulkanSDK/Include/glm/mat3x4.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double3x4.hpp  E:/VulkanSDK/Include/glm/detail/type_mat3x4.hpp  E:/VulkanSDK/Include/glm/detail/type_mat3x4.inl  E:/VulkanSDK/Include/glm/ext/matrix_double3x4_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float3x4.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float3x4_precision.hpp  E:/VulkanSDK/Include/glm/mat4x2.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double4x2.hpp  E:/VulkanSDK/Include/glm/detail/type_mat4x2.hpp  E:/VulkanSDK/Include/glm/detail/type_mat4x2.inl  E:/VulkanSDK/Include/glm/ext/matrix_double4x2_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float4x2.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float4x2_precision.hpp  E:/VulkanSDK/Include/glm/mat4x3.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double4x3.hpp  E:/VulkanSDK/Include/glm/detail/type_mat4x3.hpp  E:/VulkanSDK/Include/glm/detail/type_mat4x3.inl  E:/VulkanSDK/Include/glm/ext/matrix_double4x3_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float4x3.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float4x3_precision.hpp  E:/VulkanSDK/Include/glm/mat4x4.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double4x4.hpp  E:/VulkanSDK/Include/glm/detail/type_mat4x4.hpp  E:/VulkanSDK/Include/glm/detail/type_mat4x4.inl  E:/VulkanSDK/Include/glm/ext/matrix_double4x4_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float4x4.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float4x4_precision.hpp  E:/VulkanSDK/Include/glm/detail/func_matrix.inl  E:/VulkanSDK/Include/glm/geometric.hpp  E:/VulkanSDK/Include/glm/detail/func_geometric.inl  E:/VulkanSDK/Include/glm/exponential.hpp  E:/VulkanSDK/Include/glm/detail/type_vec1.hpp  E:/VulkanSDK/Include/glm/detail/type_vec1.inl  E:/VulkanSDK/Include/glm/detail/func_exponential.inl  E:/VulkanSDK/Include/glm/vector_relational.hpp  E:/VulkanSDK/Include/glm/detail/func_vector_relational.inl  E:/VulkanSDK/Include/glm/detail/_vectorize.hpp  E:/VulkanSDK/Include/glm/common.hpp  E:/VulkanSDK/Include/glm/detail/_fixes.hpp  E:/VulkanSDK/Include/glm/detail/func_common.inl  E:/VulkanSDK/Include/glm/detail/compute_common.hpp  E:/VulkanSDK/Include/glm/ext/matrix_double2x2_precision.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float2x2.hpp  E:/VulkanSDK/Include/glm/ext/matrix_float2x2_precision.hpp  E:\\VulkanSDK\\Include/glm/trigonometric.hpp  E:\\VulkanSDK\\Include/glm/detail/func_trigonometric.inl  E:\\VulkanSDK\\Include/glm/packing.hpp  E:\\VulkanSDK\\Include/glm/detail/func_packing.inl  E:\\VulkanSDK\\Include/glm/detail/type_half.hpp  E:\\VulkanSDK\\Include/glm/detail/type_half.inl  E:\\VulkanSDK\\Include/glm/integer.hpp  E:\\VulkanSDK\\Include/glm/detail/func_integer.inl  E:\\VulkanSDK\\Include/glm/gtc/matrix_transform.hpp  E:/VulkanSDK/Include/glm/ext/matrix_projection.hpp  E:/VulkanSDK/Include/glm/gtc/constants.hpp  E:/VulkanSDK/Include/glm/ext/scalar_constants.hpp  E:/VulkanSDK/Include/glm/ext/scalar_constants.inl  E:/VulkanSDK/Include/glm/gtc/constants.inl  E:/VulkanSDK/Include/glm/ext/matrix_projection.inl  E:/VulkanSDK/Include/glm/ext/matrix_clip_space.hpp  E:/VulkanSDK/Include/glm/ext/matrix_clip_space.inl  E:/VulkanSDK/Include/glm/ext/matrix_transform.hpp  E:/VulkanSDK/Include/glm/ext/matrix_transform.inl  E:\\VulkanSDK\\Include/glm/gtc/matrix_transform.inl\
",
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-Os",
            "-std=c++17",
            [[-IE:\VulkanSDK\Include]],
            "-fexceptions",
            "-O3",
            "-ffast-math",
            "-ftree-vectorize",
            "-funroll-loops",
            "-fomit-frame-pointer",
            "-ffunction-sections",
            "-fdata-sections",
            "-march=native"
        }
    },
    depfiles_format = "gcc"
}