#include "vulkan_renderer.h"
#include "constants.h"
#include "skybox.h"
#include "shading.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <cstring>
#include <thread>
#include <sstream>

// Static member definitions
std::mt19937 VulkanRenderer::rng(std::chrono::steady_clock::now().time_since_epoch().count());
std::uniform_int_distribution<int> VulkanRenderer::char_dist(33, 126);
std::uniform_int_distribution<int> VulkanRenderer::gray_dist(0, 255);

VulkanRenderer::VulkanRenderer(UI& ui_layer_ref) : ui_layer(ui_layer_ref) {
    if (!initializeVulkan()) {
        ui_layer.addMessage("ERROR: Failed to initialize Vulkan", RGBColor{255, 50, 50});
        vulkan_initialized = false;
    } else {
        vulkan_initialized = true;
        ui_layer.addMessage("Vulkan initialized successfully", RGBColor{100, 255, 100});
    }
}

VulkanRenderer::~VulkanRenderer() {
    cleanupVulkan();
}

bool VulkanRenderer::initializeVulkan() {
    if (!createInstance()) {
        ui_layer.addMessage("ERROR: Failed to create Vulkan instance", RGBColor{255, 50, 50});
        return false;
    }
    
    if (!selectPhysicalDevice()) {
        ui_layer.addMessage("ERROR: Failed to select physical device", RGBColor{255, 50, 50});
        return false;
    }
    
    if (!createLogicalDevice()) {
        ui_layer.addMessage("ERROR: Failed to create logical device", RGBColor{255, 50, 50});
        return false;
    }
    
    if (!createComputePipeline()) {
        ui_layer.addMessage("ERROR: Failed to create compute pipeline", RGBColor{255, 50, 50});
        return false;
    }
    
    return true;
}

bool VulkanRenderer::createInstance() {
    VkApplicationInfo app_info{};
    app_info.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
    app_info.pApplicationName = "RTerminal Vulkan Renderer";
    app_info.applicationVersion = VK_MAKE_VERSION(1, 0, 0);
    app_info.pEngineName = "RTerminal Engine";
    app_info.engineVersion = VK_MAKE_VERSION(1, 0, 0);
    app_info.apiVersion = VK_API_VERSION_1_0;
    
    VkInstanceCreateInfo create_info{};
    create_info.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
    create_info.pApplicationInfo = &app_info;
    
    // No extensions needed for headless compute
    create_info.enabledExtensionCount = 0;
    create_info.ppEnabledExtensionNames = nullptr;
    
    // No validation layers for release
    create_info.enabledLayerCount = 0;
    create_info.ppEnabledLayerNames = nullptr;
    
    VkResult result = vkCreateInstance(&create_info, nullptr, &instance);
    return result == VK_SUCCESS;
}

bool VulkanRenderer::selectPhysicalDevice() {
    uint32_t device_count = 0;
    vkEnumeratePhysicalDevices(instance, &device_count, nullptr);
    
    if (device_count == 0) {
        return false;
    }
    
    std::vector<VkPhysicalDevice> devices(device_count);
    vkEnumeratePhysicalDevices(instance, &device_count, devices.data());
    
    // Select the first device that supports compute
    for (const auto& device : devices) {
        uint32_t queue_family_count = 0;
        vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count, nullptr);
        
        std::vector<VkQueueFamilyProperties> queue_families(queue_family_count);
        vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count, queue_families.data());
        
        for (uint32_t i = 0; i < queue_family_count; i++) {
            if (queue_families[i].queueFlags & VK_QUEUE_COMPUTE_BIT) {
                physical_device = device;
                compute_queue_family = i;
                return true;
            }
        }
    }
    
    return false;
}

bool VulkanRenderer::createLogicalDevice() {
    VkDeviceQueueCreateInfo queue_create_info{};
    queue_create_info.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
    queue_create_info.queueFamilyIndex = compute_queue_family;
    queue_create_info.queueCount = 1;
    
    float queue_priority = 1.0f;
    queue_create_info.pQueuePriorities = &queue_priority;
    
    VkPhysicalDeviceFeatures device_features{};
    
    VkDeviceCreateInfo create_info{};
    create_info.sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO;
    create_info.pQueueCreateInfos = &queue_create_info;
    create_info.queueCreateInfoCount = 1;
    create_info.pEnabledFeatures = &device_features;
    create_info.enabledExtensionCount = 0;
    create_info.enabledLayerCount = 0;
    
    VkResult result = vkCreateDevice(physical_device, &create_info, nullptr, &device);
    if (result != VK_SUCCESS) {
        return false;
    }
    
    vkGetDeviceQueue(device, compute_queue_family, 0, &compute_queue);
    
    // Create command pool
    VkCommandPoolCreateInfo pool_info{};
    pool_info.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
    pool_info.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
    pool_info.queueFamilyIndex = compute_queue_family;
    
    result = vkCreateCommandPool(device, &pool_info, nullptr, &command_pool);
    if (result != VK_SUCCESS) {
        return false;
    }
    
    // Create command buffer
    VkCommandBufferAllocateInfo alloc_info{};
    alloc_info.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
    alloc_info.commandPool = command_pool;
    alloc_info.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
    alloc_info.commandBufferCount = 1;
    
    result = vkAllocateCommandBuffers(device, &alloc_info, &command_buffer);
    return result == VK_SUCCESS;
}

uint32_t VulkanRenderer::findMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties) {
    VkPhysicalDeviceMemoryProperties mem_properties;
    vkGetPhysicalDeviceMemoryProperties(physical_device, &mem_properties);
    
    for (uint32_t i = 0; i < mem_properties.memoryTypeCount; i++) {
        if ((typeFilter & (1 << i)) && 
            (mem_properties.memoryTypes[i].propertyFlags & properties) == properties) {
            return i;
        }
    }
    
    return UINT32_MAX;
}

bool VulkanRenderer::createBuffer(VkDeviceSize size, VkBufferUsageFlags usage, 
                                 VkMemoryPropertyFlags properties, VulkanBuffer& buffer) {
    VkBufferCreateInfo buffer_info{};
    buffer_info.sType = VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO;
    buffer_info.size = size;
    buffer_info.usage = usage;
    buffer_info.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
    
    VkResult result = vkCreateBuffer(device, &buffer_info, nullptr, &buffer.buffer);
    if (result != VK_SUCCESS) {
        return false;
    }
    
    VkMemoryRequirements mem_requirements;
    vkGetBufferMemoryRequirements(device, buffer.buffer, &mem_requirements);
    
    VkMemoryAllocateInfo alloc_info{};
    alloc_info.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
    alloc_info.allocationSize = mem_requirements.size;
    alloc_info.memoryTypeIndex = findMemoryType(mem_requirements.memoryTypeBits, properties);
    
    if (alloc_info.memoryTypeIndex == UINT32_MAX) {
        vkDestroyBuffer(device, buffer.buffer, nullptr);
        return false;
    }
    
    result = vkAllocateMemory(device, &alloc_info, nullptr, &buffer.memory);
    if (result != VK_SUCCESS) {
        vkDestroyBuffer(device, buffer.buffer, nullptr);
        return false;
    }
    
    vkBindBufferMemory(device, buffer.buffer, buffer.memory, 0);
    buffer.size = size;
    
    // Map memory if it's host visible
    if (properties & VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT) {
        vkMapMemory(device, buffer.memory, 0, size, 0, &buffer.mapped);
    }
    
    return true;
}

void VulkanRenderer::destroyBuffer(VulkanBuffer& buffer) {
    if (buffer.mapped) {
        vkUnmapMemory(device, buffer.memory);
        buffer.mapped = nullptr;
    }
    if (buffer.buffer != VK_NULL_HANDLE) {
        vkDestroyBuffer(device, buffer.buffer, nullptr);
        buffer.buffer = VK_NULL_HANDLE;
    }
    if (buffer.memory != VK_NULL_HANDLE) {
        vkFreeMemory(device, buffer.memory, nullptr);
        buffer.memory = VK_NULL_HANDLE;
    }
    buffer.size = 0;
}

bool VulkanRenderer::createImage(uint32_t width, uint32_t height, VkFormat format,
                                VkImageUsageFlags usage, VkMemoryPropertyFlags properties,
                                VulkanImage& image) {
    VkImageCreateInfo image_info{};
    image_info.sType = VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO;
    image_info.imageType = VK_IMAGE_TYPE_2D;
    image_info.extent.width = width;
    image_info.extent.height = height;
    image_info.extent.depth = 1;
    image_info.mipLevels = 1;
    image_info.arrayLayers = 1;
    image_info.format = format;
    image_info.tiling = VK_IMAGE_TILING_OPTIMAL;
    image_info.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    image_info.usage = usage;
    image_info.samples = VK_SAMPLE_COUNT_1_BIT;
    image_info.sharingMode = VK_SHARING_MODE_EXCLUSIVE;

    VkResult result = vkCreateImage(device, &image_info, nullptr, &image.image);
    if (result != VK_SUCCESS) {
        return false;
    }

    VkMemoryRequirements mem_requirements;
    vkGetImageMemoryRequirements(device, image.image, &mem_requirements);

    VkMemoryAllocateInfo alloc_info{};
    alloc_info.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
    alloc_info.allocationSize = mem_requirements.size;
    alloc_info.memoryTypeIndex = findMemoryType(mem_requirements.memoryTypeBits, properties);

    if (alloc_info.memoryTypeIndex == UINT32_MAX) {
        vkDestroyImage(device, image.image, nullptr);
        return false;
    }

    result = vkAllocateMemory(device, &alloc_info, nullptr, &image.memory);
    if (result != VK_SUCCESS) {
        vkDestroyImage(device, image.image, nullptr);
        return false;
    }

    vkBindImageMemory(device, image.image, image.memory, 0);

    // Create image view
    VkImageViewCreateInfo view_info{};
    view_info.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
    view_info.image = image.image;
    view_info.viewType = VK_IMAGE_VIEW_TYPE_2D;
    view_info.format = format;
    view_info.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    view_info.subresourceRange.baseMipLevel = 0;
    view_info.subresourceRange.levelCount = 1;
    view_info.subresourceRange.baseArrayLayer = 0;
    view_info.subresourceRange.layerCount = 1;

    result = vkCreateImageView(device, &view_info, nullptr, &image.view);
    if (result != VK_SUCCESS) {
        vkFreeMemory(device, image.memory, nullptr);
        vkDestroyImage(device, image.image, nullptr);
        return false;
    }

    image.format = format;
    image.width = width;
    image.height = height;

    return true;
}

void VulkanRenderer::destroyImage(VulkanImage& image) {
    if (image.view != VK_NULL_HANDLE) {
        vkDestroyImageView(device, image.view, nullptr);
        image.view = VK_NULL_HANDLE;
    }
    if (image.image != VK_NULL_HANDLE) {
        vkDestroyImage(device, image.image, nullptr);
        image.image = VK_NULL_HANDLE;
    }
    if (image.memory != VK_NULL_HANDLE) {
        vkFreeMemory(device, image.memory, nullptr);
        image.memory = VK_NULL_HANDLE;
    }
    image.format = VK_FORMAT_UNDEFINED;
    image.width = 0;
    image.height = 0;
}

std::vector<char> readFile(const std::string& filename) {
    std::ifstream file(filename, std::ios::ate | std::ios::binary);

    if (!file.is_open()) {
        return {};
    }

    size_t file_size = (size_t)file.tellg();
    std::vector<char> buffer(file_size);

    file.seekg(0);
    file.read(buffer.data(), file_size);
    file.close();

    return buffer;
}

bool VulkanRenderer::createComputePipeline() {
    // Create descriptor set layout
    VkDescriptorSetLayoutBinding bindings[7];

    // Output image
    bindings[0].binding = 0;
    bindings[0].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    bindings[0].descriptorCount = 1;
    bindings[0].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[0].pImmutableSamplers = nullptr;

    // Camera UBO
    bindings[1].binding = 1;
    bindings[1].descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    bindings[1].descriptorCount = 1;
    bindings[1].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[1].pImmutableSamplers = nullptr;

    // Material buffer
    bindings[2].binding = 2;
    bindings[2].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    bindings[2].descriptorCount = 1;
    bindings[2].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[2].pImmutableSamplers = nullptr;

    // Triangle buffer
    bindings[3].binding = 3;
    bindings[3].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    bindings[3].descriptorCount = 1;
    bindings[3].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[3].pImmutableSamplers = nullptr;

    // Light buffer
    bindings[4].binding = 4;
    bindings[4].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    bindings[4].descriptorCount = 1;
    bindings[4].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[4].pImmutableSamplers = nullptr;

    // BVH buffer
    bindings[5].binding = 5;
    bindings[5].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    bindings[5].descriptorCount = 1;
    bindings[5].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[5].pImmutableSamplers = nullptr;

    // Triangle index buffer
    bindings[6].binding = 6;
    bindings[6].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    bindings[6].descriptorCount = 1;
    bindings[6].stageFlags = VK_SHADER_STAGE_COMPUTE_BIT;
    bindings[6].pImmutableSamplers = nullptr;

    VkDescriptorSetLayoutCreateInfo layout_info{};
    layout_info.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layout_info.bindingCount = 2;
    layout_info.pBindings = bindings;

    VkResult result = vkCreateDescriptorSetLayout(device, &layout_info, nullptr, &descriptor_set_layout);
    if (result != VK_SUCCESS) {
        return false;
    }

    // Create pipeline layout
    VkPipelineLayoutCreateInfo pipeline_layout_info{};
    pipeline_layout_info.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    pipeline_layout_info.setLayoutCount = 1;
    pipeline_layout_info.pSetLayouts = &descriptor_set_layout;

    result = vkCreatePipelineLayout(device, &pipeline_layout_info, nullptr, &pipeline_layout);
    if (result != VK_SUCCESS) {
        return false;
    }

    // Load compute shader
    auto shader_code = readFile("shaders/raytracer.spv");
    if (shader_code.empty()) {
        ui_layer.addMessage("ERROR: Failed to load compute shader", RGBColor{255, 50, 50});
        return false;
    }

    VkShaderModuleCreateInfo shader_info{};
    shader_info.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    shader_info.codeSize = shader_code.size();
    shader_info.pCode = reinterpret_cast<const uint32_t*>(shader_code.data());

    VkShaderModule shader_module;
    result = vkCreateShaderModule(device, &shader_info, nullptr, &shader_module);
    if (result != VK_SUCCESS) {
        return false;
    }

    // Create compute pipeline
    VkPipelineShaderStageCreateInfo stage_info{};
    stage_info.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    stage_info.stage = VK_SHADER_STAGE_COMPUTE_BIT;
    stage_info.module = shader_module;
    stage_info.pName = "main";

    VkComputePipelineCreateInfo pipeline_info{};
    pipeline_info.sType = VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO;
    pipeline_info.stage = stage_info;
    pipeline_info.layout = pipeline_layout;

    result = vkCreateComputePipelines(device, VK_NULL_HANDLE, 1, &pipeline_info, nullptr, &compute_pipeline);

    vkDestroyShaderModule(device, shader_module, nullptr);

    return result == VK_SUCCESS;
}

bool VulkanRenderer::createDescriptorSets() {
    // Create descriptor pool
    VkDescriptorPoolSize pool_sizes[3];
    pool_sizes[0].type = VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    pool_sizes[0].descriptorCount = 1;
    pool_sizes[1].type = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    pool_sizes[1].descriptorCount = 1;
    pool_sizes[2].type = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    pool_sizes[2].descriptorCount = 5; // materials, triangles, lights, bvh, triangle_indices

    VkDescriptorPoolCreateInfo pool_info{};
    pool_info.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
    pool_info.poolSizeCount = 2;
    pool_info.pPoolSizes = pool_sizes;
    pool_info.maxSets = 1;

    VkResult result = vkCreateDescriptorPool(device, &pool_info, nullptr, &descriptor_pool);
    if (result != VK_SUCCESS) {
        return false;
    }

    // Allocate descriptor set
    VkDescriptorSetAllocateInfo alloc_info{};
    alloc_info.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
    alloc_info.descriptorPool = descriptor_pool;
    alloc_info.descriptorSetCount = 1;
    alloc_info.pSetLayouts = &descriptor_set_layout;

    result = vkAllocateDescriptorSets(device, &alloc_info, &descriptor_set);
    return result == VK_SUCCESS;
}

void VulkanRenderer::updateDescriptorSets() {
    VkWriteDescriptorSet writes[7];

    // Output image
    VkDescriptorImageInfo image_info{};
    image_info.imageView = output_image.view;
    image_info.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

    writes[0].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[0].pNext = nullptr;
    writes[0].dstSet = descriptor_set;
    writes[0].dstBinding = 0;
    writes[0].dstArrayElement = 0;
    writes[0].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    writes[0].descriptorCount = 1;
    writes[0].pImageInfo = &image_info;

    // Camera buffer
    VkDescriptorBufferInfo camera_info{};
    camera_info.buffer = camera_buffer.buffer;
    camera_info.offset = 0;
    camera_info.range = VK_WHOLE_SIZE;

    writes[1].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[1].pNext = nullptr;
    writes[1].dstSet = descriptor_set;
    writes[1].dstBinding = 1;
    writes[1].dstArrayElement = 0;
    writes[1].descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    writes[1].descriptorCount = 1;
    writes[1].pBufferInfo = &camera_info;

    // Material buffer
    VkDescriptorBufferInfo material_info{};
    material_info.buffer = material_buffer.buffer;
    material_info.offset = 0;
    material_info.range = VK_WHOLE_SIZE;

    writes[2].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[2].pNext = nullptr;
    writes[2].dstSet = descriptor_set;
    writes[2].dstBinding = 2;
    writes[2].dstArrayElement = 0;
    writes[2].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    writes[2].descriptorCount = 1;
    writes[2].pBufferInfo = &material_info;

    // Triangle buffer
    VkDescriptorBufferInfo triangle_info{};
    triangle_info.buffer = triangle_buffer.buffer;
    triangle_info.offset = 0;
    triangle_info.range = VK_WHOLE_SIZE;

    writes[3].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[3].pNext = nullptr;
    writes[3].dstSet = descriptor_set;
    writes[3].dstBinding = 3;
    writes[3].dstArrayElement = 0;
    writes[3].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    writes[3].descriptorCount = 1;
    writes[3].pBufferInfo = &triangle_info;

    // Light buffer
    VkDescriptorBufferInfo light_info{};
    light_info.buffer = light_buffer.buffer;
    light_info.offset = 0;
    light_info.range = VK_WHOLE_SIZE;

    writes[4].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[4].pNext = nullptr;
    writes[4].dstSet = descriptor_set;
    writes[4].dstBinding = 4;
    writes[4].dstArrayElement = 0;
    writes[4].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    writes[4].descriptorCount = 1;
    writes[4].pBufferInfo = &light_info;

    // BVH buffer
    VkDescriptorBufferInfo bvh_info{};
    bvh_info.buffer = bvh_buffer.buffer;
    bvh_info.offset = 0;
    bvh_info.range = VK_WHOLE_SIZE;

    writes[5].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[5].pNext = nullptr;
    writes[5].dstSet = descriptor_set;
    writes[5].dstBinding = 5;
    writes[5].dstArrayElement = 0;
    writes[5].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    writes[5].descriptorCount = 1;
    writes[5].pBufferInfo = &bvh_info;

    // Triangle index buffer
    VkDescriptorBufferInfo index_info{};
    index_info.buffer = triangle_index_buffer.buffer;
    index_info.offset = 0;
    index_info.range = VK_WHOLE_SIZE;

    writes[6].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[6].pNext = nullptr;
    writes[6].dstSet = descriptor_set;
    writes[6].dstBinding = 6;
    writes[6].dstArrayElement = 0;
    writes[6].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    writes[6].descriptorCount = 1;
    writes[6].pBufferInfo = &index_info;

    // Only update camera buffer if it exists
    int write_count = 1;
    if (camera_buffer.buffer != VK_NULL_HANDLE) {
        write_count = 2;
    }

    vkUpdateDescriptorSets(device, write_count, writes, 0, nullptr);
}

void VulkanRenderer::uploadSceneData(const Scene& scene) {
    // Convert materials
    std::vector<GPUMaterial> gpu_materials;
    for (const auto& mat_pair : scene.materials) {
        GPUMaterial gpu_mat{};
        gpu_mat.color = mat_pair.second.color;
        gpu_mat.ambient = mat_pair.second.ambient;
        gpu_mat.diffuse = mat_pair.second.diffuse;
        gpu_mat.specular = mat_pair.second.specular;
        gpu_mat.shininess = mat_pair.second.shininess;
        gpu_mat.texture_id = -1; // No texture support for now
        gpu_mat.symbol = mat_pair.second.symbol;
        gpu_materials.push_back(gpu_mat);
    }

    // Convert triangles
    std::vector<GPUTriangle> gpu_triangles;
    int triangle_count = 0;
    for (const auto& obj : scene.objects) {
        if (auto triangle = dynamic_cast<const Triangle*>(obj.get())) {
            triangle_count++;
            GPUTriangle gpu_tri{};
            gpu_tri.v0 = triangle->v0;
            gpu_tri.v1 = triangle->v1;
            gpu_tri.v2 = triangle->v2;
            gpu_tri.normal = triangle->faceNormal;
            gpu_tri.uv0 = triangle->uv0;
            gpu_tri.uv1 = triangle->uv1;
            gpu_tri.uv2 = triangle->uv2;

            // Find material index
            gpu_tri.material_id = 0;
            int mat_idx = 0;
            for (const auto& mat_pair : scene.materials) {
                if (mat_pair.second.symbol == triangle->material.symbol &&
                    mat_pair.second.color.x == triangle->material.color.x &&
                    mat_pair.second.color.y == triangle->material.color.y &&
                    mat_pair.second.color.z == triangle->material.color.z) {
                    gpu_tri.material_id = mat_idx;
                    break;
                }
                mat_idx++;
            }

            gpu_triangles.push_back(gpu_tri);
        }
    }

    // Convert lights
    std::vector<GPULight> gpu_lights;
    for (const auto& light : scene.lights) {
        GPULight gpu_light{};
        gpu_light.position = light.position;
        gpu_light.color = light.color;
        gpu_light.intensity = light.intensity;
        gpu_lights.push_back(gpu_light);
    }

    // Debug output
    std::string debug_msg = "Found " + std::to_string(triangle_count) + " triangles, " +
                           std::to_string(gpu_materials.size()) + " materials, " +
                           std::to_string(gpu_lights.size()) + " lights";
    ui_layer.addMessage(debug_msg, RGBColor{100, 200, 100});

    // Convert BVH (simplified - just create a single node for now)
    std::vector<GPUBVHNode> gpu_bvh_nodes;
    std::vector<int> gpu_triangle_indices;

    if (!gpu_triangles.empty()) {
        // Calculate proper AABB bounds from all triangles
        Vec3 scene_min = gpu_triangles[0].v0;
        Vec3 scene_max = gpu_triangles[0].v0;

        for (const auto& tri : gpu_triangles) {
            // Check all three vertices of each triangle
            for (const Vec3& vertex : {tri.v0, tri.v1, tri.v2}) {
                scene_min.x = std::min(scene_min.x, vertex.x);
                scene_min.y = std::min(scene_min.y, vertex.y);
                scene_min.z = std::min(scene_min.z, vertex.z);
                scene_max.x = std::max(scene_max.x, vertex.x);
                scene_max.y = std::max(scene_max.y, vertex.y);
                scene_max.z = std::max(scene_max.z, vertex.z);
            }
        }

        // Add small padding to avoid edge cases
        Vec3 padding(0.1, 0.1, 0.1);
        scene_min = scene_min - padding;
        scene_max = scene_max + padding;

        GPUBVHNode root_node{};
        root_node.aabb_min = scene_min;
        root_node.aabb_max = scene_max;
        root_node.left_child = -1;
        root_node.right_child = -1;
        root_node.triangle_start = 0;
        root_node.triangle_count = gpu_triangles.size();
        gpu_bvh_nodes.push_back(root_node);

        for (int i = 0; i < gpu_triangles.size(); i++) {
            gpu_triangle_indices.push_back(i);
        }
    }

    // Create buffers and upload data
    if (!gpu_materials.empty()) {
        createBuffer(gpu_materials.size() * sizeof(GPUMaterial),
                    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    material_buffer);
        memcpy(material_buffer.mapped, gpu_materials.data(), gpu_materials.size() * sizeof(GPUMaterial));
    }

    if (!gpu_triangles.empty()) {
        createBuffer(gpu_triangles.size() * sizeof(GPUTriangle),
                    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    triangle_buffer);
        memcpy(triangle_buffer.mapped, gpu_triangles.data(), gpu_triangles.size() * sizeof(GPUTriangle));
    }

    if (!gpu_lights.empty()) {
        createBuffer(gpu_lights.size() * sizeof(GPULight),
                    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    light_buffer);
        memcpy(light_buffer.mapped, gpu_lights.data(), gpu_lights.size() * sizeof(GPULight));
    }

    if (!gpu_bvh_nodes.empty()) {
        createBuffer(gpu_bvh_nodes.size() * sizeof(GPUBVHNode),
                    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    bvh_buffer);
        memcpy(bvh_buffer.mapped, gpu_bvh_nodes.data(), gpu_bvh_nodes.size() * sizeof(GPUBVHNode));
    }

    if (!gpu_triangle_indices.empty()) {
        createBuffer(gpu_triangle_indices.size() * sizeof(int),
                    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    triangle_index_buffer);
        memcpy(triangle_index_buffer.mapped, gpu_triangle_indices.data(), gpu_triangle_indices.size() * sizeof(int));
    }
}

void VulkanRenderer::uploadCameraData(const Vec3& camera_pos, const Vec3& camera_dir,
                                     const Vec3& camera_up, double fov, int width, int height) {
    CameraUBO camera_ubo{};
    camera_ubo.position = camera_pos;

    // Ensure proper orthogonal camera basis (like original renderer)
    const Vec3 cam_fwd = camera_dir.normalize();
    const Vec3 cam_right = cam_fwd.cross(camera_up).normalize();
    const Vec3 cam_up = cam_right.cross(cam_fwd).normalize();

    camera_ubo.direction = cam_fwd;
    camera_ubo.up = cam_up;
    camera_ubo.right = cam_right;
    camera_ubo.fov = fov; // Keep in degrees for now
    camera_ubo.aspect_ratio = (float)width / (float)height;
    camera_ubo.width = width;
    camera_ubo.height = height;

    if (camera_buffer.buffer == VK_NULL_HANDLE) {
        createBuffer(sizeof(CameraUBO),
                    VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    camera_buffer);
    }

    memcpy(camera_buffer.mapped, &camera_ubo, sizeof(CameraUBO));
}

void VulkanRenderer::dispatch(int width, int height) {
    // Create or recreate output image if needed
    if (output_image.image == VK_NULL_HANDLE ||
        output_image.width != width || output_image.height != height) {

        if (output_image.image != VK_NULL_HANDLE) {
            destroyImage(output_image);
        }

        createImage(width, height, VK_FORMAT_R8G8B8A8_UNORM,
                   VK_IMAGE_USAGE_STORAGE_BIT | VK_IMAGE_USAGE_TRANSFER_SRC_BIT,
                   VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT,
                   output_image);
    }

    // Create staging buffer for readback if needed
    VkDeviceSize image_size = width * height * 4; // RGBA
    if (staging_buffer.buffer == VK_NULL_HANDLE || staging_buffer.size < image_size) {
        if (staging_buffer.buffer != VK_NULL_HANDLE) {
            destroyBuffer(staging_buffer);
        }

        createBuffer(image_size,
                    VK_BUFFER_USAGE_TRANSFER_DST_BIT,
                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                    staging_buffer);
    }

    // Create descriptor sets if needed
    if (descriptor_set == VK_NULL_HANDLE) {
        createDescriptorSets();
    }

    // Update descriptor sets
    updateDescriptorSets();

    // Record command buffer
    VkCommandBufferBeginInfo begin_info{};
    begin_info.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    begin_info.flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT;

    vkBeginCommandBuffer(command_buffer, &begin_info);

    // Transition image to general layout
    VkImageMemoryBarrier barrier{};
    barrier.sType = VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER;
    barrier.oldLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    barrier.newLayout = VK_IMAGE_LAYOUT_GENERAL;
    barrier.srcQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.dstQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.image = output_image.image;
    barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    barrier.subresourceRange.baseMipLevel = 0;
    barrier.subresourceRange.levelCount = 1;
    barrier.subresourceRange.baseArrayLayer = 0;
    barrier.subresourceRange.layerCount = 1;
    barrier.srcAccessMask = 0;
    barrier.dstAccessMask = VK_ACCESS_SHADER_WRITE_BIT;

    vkCmdPipelineBarrier(command_buffer,
                        VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT,
                        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,
                        0, 0, nullptr, 0, nullptr, 1, &barrier);

    // Bind compute pipeline and descriptor sets
    vkCmdBindPipeline(command_buffer, VK_PIPELINE_BIND_POINT_COMPUTE, compute_pipeline);
    vkCmdBindDescriptorSets(command_buffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           pipeline_layout, 0, 1, &descriptor_set, 0, nullptr);

    // Dispatch compute shader
    uint32_t group_count_x = (width + 7) / 8;
    uint32_t group_count_y = (height + 7) / 8;
    vkCmdDispatch(command_buffer, group_count_x, group_count_y, 1);

    // Transition image for transfer
    barrier.oldLayout = VK_IMAGE_LAYOUT_GENERAL;
    barrier.newLayout = VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL;
    barrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;
    barrier.dstAccessMask = VK_ACCESS_TRANSFER_READ_BIT;

    vkCmdPipelineBarrier(command_buffer,
                        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,
                        VK_PIPELINE_STAGE_TRANSFER_BIT,
                        0, 0, nullptr, 0, nullptr, 1, &barrier);

    // Copy image to staging buffer
    VkBufferImageCopy region{};
    region.bufferOffset = 0;
    region.bufferRowLength = 0;
    region.bufferImageHeight = 0;
    region.imageSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    region.imageSubresource.mipLevel = 0;
    region.imageSubresource.baseArrayLayer = 0;
    region.imageSubresource.layerCount = 1;
    region.imageOffset = {0, 0, 0};
    region.imageExtent = {(uint32_t)width, (uint32_t)height, 1};

    vkCmdCopyImageToBuffer(command_buffer, output_image.image,
                          VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
                          staging_buffer.buffer, 1, &region);

    vkEndCommandBuffer(command_buffer);

    // Submit command buffer
    VkSubmitInfo submit_info{};
    submit_info.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submit_info.commandBufferCount = 1;
    submit_info.pCommandBuffers = &command_buffer;

    VkResult result = vkQueueSubmit(compute_queue, 1, &submit_info, VK_NULL_HANDLE);
    if (result != VK_SUCCESS) {
        ui_layer.addMessage("ERROR: Failed to submit compute command buffer", RGBColor{255, 50, 50});
        return;
    }

    result = vkQueueWaitIdle(compute_queue);
    if (result != VK_SUCCESS) {
        ui_layer.addMessage("ERROR: Failed to wait for compute queue", RGBColor{255, 50, 50});
        return;
    }

    ui_layer.addMessage("Compute shader dispatched successfully", RGBColor{100, 200, 100});
}

void VulkanRenderer::convertToConsoleBuffer(int width, int height) {
    // Initialize buffers if needed
    if (current_buffer.size() != height || (height > 0 && current_buffer[0].size() != width)) {
        current_buffer.resize(height);
        previous_buffer.resize(height);
        for (int y = 0; y < height; y++) {
            current_buffer[y].resize(width);
            previous_buffer[y].resize(width);
        }
    }

    // Read pixel data from staging buffer
    uint8_t* pixel_data = static_cast<uint8_t*>(staging_buffer.mapped);

    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int pixel_index = (y * width + x) * 4; // RGBA
            uint8_t r = pixel_data[pixel_index + 0];
            uint8_t g = pixel_data[pixel_index + 1];
            uint8_t b = pixel_data[pixel_index + 2];

            RGBColor color{r, g, b};

            ConsoleCell& cell = current_buffer[y][x];
            cell.color = color;

            if (ui_layer.isPixelMode()) {
                cell.character = ' '; // Use space for pixel mode
            } else {
                cell.character = colorToAsciiChar(color);
            }

            cell.is_ui = false;
        }
    }
}

char VulkanRenderer::colorToAsciiChar(const RGBColor& color) {
    // Calculate luminance
    double luminance = 0.2126 * color.r + 0.7152 * color.g + 0.0722 * color.b;
    luminance /= 255.0; // Normalize to [0, 1]

    const std::string& ascii_ramp = ASCII_RAMP;
    luminance = std::clamp(luminance, 0.0, 1.0);
    return ascii_ramp[static_cast<size_t>(luminance * (ascii_ramp.length() - 1))];
}

std::vector<UpdateCommand> VulkanRenderer::generateUpdateCommands(int width, int height) {
    std::vector<UpdateCommand> update_commands;
    update_commands.reserve(static_cast<size_t>(width) * height / 4);

    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            if (y >= current_buffer.size() || x >= current_buffer[y].size()) continue;
            if (y >= previous_buffer.size() || x >= previous_buffer[y].size()) continue;

            const ConsoleCell& current = current_buffer[y][x];
            const ConsoleCell& previous = previous_buffer[y][x];

            if (first_frame ||
                current.character != previous.character ||
                current.color.r != previous.color.r ||
                current.color.g != previous.color.g ||
                current.color.b != previous.color.b) {

                UpdateCommand cmd;
                cmd.x = x;
                cmd.y = y;
                cmd.character = current.character;
                cmd.target_fg = current.color;
                cmd.target_bg = RGBColor{0, 0, 0}; // Default background
                update_commands.push_back(cmd);
            }
        }
    }

    // Copy current to previous
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            if (y < current_buffer.size() && x < current_buffer[y].size() &&
                y < previous_buffer.size() && x < previous_buffer[y].size()) {
                previous_buffer[y][x] = current_buffer[y][x];
            }
        }
    }

    return update_commands;
}

void VulkanRenderer::outputUpdateCommands(const std::vector<UpdateCommand>& update_commands, int height) {
    if (update_commands.empty()) return;

    std::stringstream final_output_ss;
    final_output_ss.str("");
    final_output_ss.clear();

    RGBColor last_color = {255, 255, 255};
    bool color_set = false;

    for (const auto& cmd : update_commands) {
        // Move cursor to position
        final_output_ss << "\x1b[" << (cmd.y + ANSI_CURSOR_MOVE_BASE) << ";"
                       << (cmd.x + ANSI_CURSOR_MOVE_BASE) << "H";

        // Set color if different from last
        if (!color_set || cmd.target_fg.r != last_color.r ||
            cmd.target_fg.g != last_color.g || cmd.target_fg.b != last_color.b) {

            if (ui_layer.isPixelMode()) {
                final_output_ss << "\x1b[48;2;" << (int)cmd.target_fg.r << ";"
                               << (int)cmd.target_fg.g << ";" << (int)cmd.target_fg.b << "m";
            } else {
                final_output_ss << "\x1b[38;2;" << (int)cmd.target_fg.r << ";"
                               << (int)cmd.target_fg.g << ";" << (int)cmd.target_fg.b << "m";
            }

            last_color = cmd.target_fg;
            color_set = true;
        }

        // Output character
        final_output_ss << cmd.character;
    }

    // Reset and move cursor
    final_output_ss << ANSI_RESET;
    final_output_ss << "\x1b[" << (height + ANSI_CURSOR_MOVE_BASE) << ";1H";

    std::cout << final_output_ss.str() << std::flush;
}

void VulkanRenderer::render(const Scene& scene, int width, int height, const Vec3& camera_pos,
                           const Vec3& camera_dir, const Vec3& camera_up, double fov, double fps,
                           bool render_ui, bool is_loading) {

    if (!vulkan_initialized) {
        ui_layer.addMessage("ERROR: Vulkan not initialized", RGBColor{255, 50, 50});
        return;
    }

    // Initialize buffers if needed
    if (current_buffer.size() != height || (height > 0 && current_buffer[0].size() != width)) {
        current_buffer.resize(height);
        previous_buffer.resize(height);
        for (int y = 0; y < height; y++) {
            current_buffer[y].resize(width);
            previous_buffer[y].resize(width);
        }
        first_frame = true;
    }

    if (first_frame) {
        clearConsole();
    }

    // Apply transition effect if in transition or loading
    if (is_loading || render_mode_transition_frames > 0) {
        // Fill with static effect
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                if (y >= 0 && y < current_buffer.size() && x >= 0 && x < current_buffer[y].size()) {
                    uint8_t gray_value = (uint8_t)gray_dist(rng);
                    current_buffer[y][x].character = static_cast<char>(char_dist(rng));
                    current_buffer[y][x].color = RGBColor{gray_value, gray_value, gray_value};
                    current_buffer[y][x].is_ui = false;
                }
            }
        }
    } else {
        // Upload scene data (only once or when scene changes)
        static bool scene_uploaded = false;
        if (!scene_uploaded) {
            ui_layer.addMessage("Uploading scene data to GPU...", RGBColor{100, 200, 100});
            uploadSceneData(scene);
            scene_uploaded = true;

            // Debug: Show how many triangles were uploaded
            std::string debug_msg = "Uploaded " + std::to_string(scene.objects.size()) + " objects to GPU";
            ui_layer.addMessage(debug_msg, RGBColor{100, 200, 100});
        }

        // Upload camera data
        uploadCameraData(camera_pos, camera_dir, camera_up, fov, width, height);

        // Dispatch compute shader
        dispatch(width, height);

        // Convert GPU output to console buffer
        convertToConsoleBuffer(width, height);
    }

    // Render UI on top
    if (render_ui || is_loading || render_mode_transition_frames > 0) {
        ui_layer.render(current_buffer, width, height);
    }

    // Generate and output update commands
    std::vector<UpdateCommand> update_commands = generateUpdateCommands(width, height);
    outputUpdateCommands(update_commands, height);

    if (!is_loading) {
        first_frame = false;
    }
}

void VulkanRenderer::toggleNormalsVisualization() {
    show_normals_mode = !show_normals_mode;
    first_frame = true;
    render_mode_transition_frames = TRANSITION_DURATION_FRAMES;
}

void VulkanRenderer::startTransition() {
    render_mode_transition_frames = TRANSITION_DURATION_FRAMES;
    first_frame = true;
}

void VulkanRenderer::updateTransition() {
    if (render_mode_transition_frames > 0) {
        render_mode_transition_frames--;
        if (render_mode_transition_frames == 0) {
            first_frame = true;
        }
    }
}

void VulkanRenderer::updateAnimations(double deltaTime, double time_speed_multiplier, const Vec3& cloud_velocity) {
    cloud_animation_offset = cloud_animation_offset + cloud_velocity * deltaTime * time_speed_multiplier;
    current_time += deltaTime * time_speed_multiplier;
}

void VulkanRenderer::cleanupVulkan() {
    if (device != VK_NULL_HANDLE) {
        vkDeviceWaitIdle(device);

        destroyBuffer(camera_buffer);
        destroyBuffer(material_buffer);
        destroyBuffer(triangle_buffer);
        destroyBuffer(light_buffer);
        destroyBuffer(bvh_buffer);
        destroyBuffer(triangle_index_buffer);
        destroyBuffer(staging_buffer);
        destroyImage(output_image);

        if (compute_pipeline != VK_NULL_HANDLE) {
            vkDestroyPipeline(device, compute_pipeline, nullptr);
        }
        if (pipeline_layout != VK_NULL_HANDLE) {
            vkDestroyPipelineLayout(device, pipeline_layout, nullptr);
        }
        if (descriptor_pool != VK_NULL_HANDLE) {
            vkDestroyDescriptorPool(device, descriptor_pool, nullptr);
        }
        if (descriptor_set_layout != VK_NULL_HANDLE) {
            vkDestroyDescriptorSetLayout(device, descriptor_set_layout, nullptr);
        }
        if (command_pool != VK_NULL_HANDLE) {
            vkDestroyCommandPool(device, command_pool, nullptr);
        }

        vkDestroyDevice(device, nullptr);
    }

    if (instance != VK_NULL_HANDLE) {
        vkDestroyInstance(instance, nullptr);
    }
}
