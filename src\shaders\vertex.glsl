#version 450

// Vertex attributes
layout(location = 0) in vec3 inPosition;
layout(location = 1) in vec3 inNormal;
layout(location = 2) in vec2 inTexCoord;

// Uniform buffer
layout(binding = 0) uniform UniformBufferObject {
    mat4 model;
    mat4 view;
    mat4 proj;
    vec3 cameraPos;
    float time;
    vec3 lightPos;
    float padding;
    vec3 lightColor;
    float lightIntensity;
} ubo;

// Output to fragment shader
layout(location = 0) out vec3 fragWorldPos;
layout(location = 1) out vec3 fragNormal;
layout(location = 2) out vec2 fragTexCoord;
layout(location = 3) out vec3 fragCameraPos;
layout(location = 4) out vec3 fragLightPos;
layout(location = 5) out vec3 fragLightColor;
layout(location = 6) out float fragLightIntensity;

void main() {
    // Transform vertex position to world space
    vec4 worldPos = ubo.model * vec4(inPosition, 1.0);
    fragWorldPos = worldPos.xyz;
    
    // Transform normal to world space
    fragNormal = normalize(mat3(ubo.model) * inNormal);
    
    // Pass through texture coordinates
    fragTexCoord = inTexCoord;
    
    // Pass camera and light data
    fragCameraPos = ubo.cameraPos;
    fragLightPos = ubo.lightPos;
    fragLightColor = ubo.lightColor;
    fragLightIntensity = ubo.lightIntensity;
    
    // Transform to clip space
    gl_Position = ubo.proj * ubo.view * worldPos;
}
