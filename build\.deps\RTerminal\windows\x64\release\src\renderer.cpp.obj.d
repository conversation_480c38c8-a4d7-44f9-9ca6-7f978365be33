{
    depfiles_format = "gcc",
    depfiles = "renderer.o: src\\renderer.cpp src\\renderer.h src\\console.h src\\vector.h  src\\constants.h src\\scene.h src\\triangle.h src\\object.h src\\ray.h  src\\aabb.h src\\material.h src\\texture.h src\\ui.h src\\light.h src\\bvh.h  src\\console_utils.h src\\shading.h src\\skybox.h src\\collision_utils.h\
",
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-Os",
            "-std=c++17",
            [[-IE:\VulkanSDK\Include]],
            "-fexceptions",
            "-O3",
            "-ffast-math",
            "-ftree-vectorize",
            "-funroll-loops",
            "-fomit-frame-pointer",
            "-ffunction-sections",
            "-fdata-sections",
            "-march=native"
        }
    },
    files = {
        [[src\renderer.cpp]]
    }
}