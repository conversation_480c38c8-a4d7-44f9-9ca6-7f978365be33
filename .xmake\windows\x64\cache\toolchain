{
    tool_target_RTerminal_windows_x64_cxx = {
        toolname = "gxx",
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        toolchain_info = {
            plat = "windows",
            name = "mingw",
            arch = "x64",
            cachekey = "mingw_arch_x64_plat_windows"
        }
    },
    tool_target_RTerminal_windows_x64_ld = {
        toolname = "gxx",
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        toolchain_info = {
            plat = "windows",
            name = "mingw",
            arch = "x64",
            cachekey = "mingw_arch_x64_plat_windows"
        }
    },
    mingw_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows",
        bindir = [[C:\msys64\mingw64\bin]],
        mingw = [[C:\msys64\mingw64]],
        cross = "x86_64-w64-mingw32-"
    },
    tool_target_RTerminal_windows_x64_sh = {
        toolname = "gxx",
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        toolchain_info = {
            plat = "windows",
            name = "mingw",
            arch = "x64",
            cachekey = "mingw_arch_x64_plat_windows"
        }
    },
    tool_target_RTerminal_windows_x64_cc = {
        toolname = "gcc",
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]],
        toolchain_info = {
            plat = "windows",
            name = "mingw",
            arch = "x64",
            cachekey = "mingw_arch_x64_plat_windows"
        }
    }
}