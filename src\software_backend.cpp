#include "software_backend.h"
#include "constants.h"
#include <algorithm>
#include <cmath>
#include <limits>

SoftwareBackend::SoftwareBackend(bool& show_normals_mode_ref, bool& use_background_color_mode_ref,
                                Vec3& cloud_animation_offset_ref, double& current_time_ref)
    : show_normals_mode(show_normals_mode_ref)
    , use_background_color_mode(use_background_color_mode_ref)
    , cloud_animation_offset(cloud_animation_offset_ref)
    , current_time(current_time_ref)
{
    render_tasks.resize(MAX_RENDER_THREADS);
}

bool SoftwareBackend::initialize() {
    // Software backend doesn't need special initialization
    return true;
}

void SoftwareBackend::cleanup() {
    // Wait for any pending render tasks to complete
    for (auto& task : render_tasks) {
        if (task.active && task.future.valid()) {
            task.future.wait();
            task.active = false;
        }
    }
}

ConsoleCell SoftwareBackend::castRay(const Ray& ray, const Scene& scene) {
    double min_t = std::numeric_limits<double>::max();
    const Object* hit_object = nullptr;
    Vec3 hit_normal;
    double hit_u = 0.0, hit_v = 0.0;
    bool hit = scene.bvh.intersect(ray, min_t, hit_object, hit_normal, hit_u, hit_v);

    if (hit) {
        // Hit scene geometry
        if (!hit_object) {
            return ConsoleCell();
        }
        Vec3 hit_point = ray.origin + ray.direction * min_t;
        return shade(hit_point, hit_normal, hit_object->material, scene, hit_u, hit_v, show_normals_mode);
    } else {
        // Skybox logic (ray missed scene geometry)
        if (show_normals_mode) {
            ConsoleCell normal_sky_cell;
            normal_sky_cell.character = ' ';
            normal_sky_cell.color = RGBColor{0, 0, 0};
            return normal_sky_cell;
        }
        return Skybox::getColor(ray, scene, cloud_animation_offset, use_background_color_mode, current_time, 0.0);
    }
}

void SoftwareBackend::renderScene(const Scene& scene, int width, int height, 
                                 const Vec3& camera_pos, const Vec3& camera_dir, 
                                 const Vec3& camera_up, double fov,
                                 std::vector<std::vector<ConsoleCell>>& output_buffer) {
    
    // Ensure output buffer is properly sized
    if (output_buffer.size() != static_cast<size_t>(height) || 
        (height > 0 && output_buffer[0].size() != static_cast<size_t>(width))) {
        output_buffer.assign(height, std::vector<ConsoleCell>(width));
    }

    // Skip rendering if dimensions are invalid
    if (width <= 0 || height <= 0) {
        return;
    }

    // Calculate camera parameters
    double aspect_ratio = static_cast<double>(width) / height;
    double half_view_height = std::tan(fov * 0.5 * M_PI / 180.0);
    double half_view_width = aspect_ratio * half_view_height;

    Vec3 cam_fwd = camera_dir.normalize();
    Vec3 cam_right = cam_fwd.cross(camera_up).normalize();
    Vec3 cam_up = cam_right.cross(cam_fwd).normalize();

    // Multi-threaded rendering
    const int num_threads = std::min(MAX_RENDER_THREADS, std::max(1, static_cast<int>(std::thread::hardware_concurrency())));
    const int rows_per_thread = std::max(1, height / num_threads);

    // Wait for any previous render tasks to complete
    for (auto& task : render_tasks) {
        if (task.active && task.future.valid()) {
            task.future.wait();
            task.active = false;
        }
    }

    // Launch render tasks
    int current_start_row = 0;
    for (int i = 0; i < num_threads && current_start_row < height; ++i) {
        int task_end_row = std::min(current_start_row + rows_per_thread, height);
        
        if (task_end_row > current_start_row) {
            render_tasks[i].active = true;
            
            // Capture necessary variables for the lambda
            render_tasks[i].future = std::async(std::launch::async,
                [&scene, width, height, camera_pos, cam_fwd, cam_up, cam_right, 
                 half_view_width, half_view_height, task_start_row = current_start_row, task_end_row, this]()
                -> std::vector<std::vector<ConsoleCell>>
                {
                    std::vector<std::vector<ConsoleCell>> task_rows_buffer(task_end_row - task_start_row, std::vector<ConsoleCell>(width));
                    
                    for (int y = task_start_row; y < task_end_row; ++y) {
                        int local_y = y - task_start_row;
                        for (int x = 0; x < width; ++x) {
                            // Calculate normalized device coordinates [-1, 1]
                            double ndc_x = (2.0 * (x + 0.5) / width - 1.0);
                            double ndc_y = (1.0 - 2.0 * (y + 0.5) / height);
                            
                            // Calculate ray direction in world space
                            Vec3 ray_direction = (cam_fwd
                                + cam_right * (ndc_x * half_view_width)
                                + cam_up * (ndc_y * half_view_height)
                                ).normalize();
                            
                            Ray ray(camera_pos, ray_direction);
                            task_rows_buffer[local_y][x] = this->castRay(ray, scene);
                        }
                    }
                    return task_rows_buffer;
                });
            
            current_start_row = task_end_row;
        }
    }

    // Collect results from all tasks
    current_start_row = 0;
    for (int i = 0; i < num_threads && current_start_row < height; ++i) {
        if (render_tasks[i].active && render_tasks[i].future.valid()) {
            auto task_result = render_tasks[i].future.get();
            
            // Copy task results to output buffer
            for (size_t local_y = 0; local_y < task_result.size(); ++local_y) {
                int global_y = current_start_row + static_cast<int>(local_y);
                if (global_y < height) {
                    output_buffer[global_y] = std::move(task_result[local_y]);
                }
            }
            
            render_tasks[i].active = false;
            current_start_row += static_cast<int>(task_result.size());
        }
    }
}
