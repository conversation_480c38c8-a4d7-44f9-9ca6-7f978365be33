#ifndef VULKAN_BACKEND_H
#define VULKAN_BACKEND_H

#include "renderer.h"
#include "console.h"
#include "scene.h"
#include "vector.h"
#include <vector>
#include <memory>

#ifdef _WIN32
#define VK_USE_PLATFORM_WIN32_KHR
#endif

#include <vulkan/vulkan.h>

// GLM for matrix operations
#define GLM_FORCE_RADIANS
#define GLM_FORCE_DEPTH_ZERO_TO_ONE
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

// Uniform buffer object structure
struct UniformBufferObject {
    alignas(16) glm::mat4 model;
    alignas(16) glm::mat4 view;
    alignas(16) glm::mat4 proj;
    alignas(16) glm::vec3 cameraPos;
    alignas(4) float time;
    alignas(16) glm::vec3 lightPos;
    alignas(4) float padding;
    alignas(16) glm::vec3 lightColor;
    alignas(4) float lightIntensity;
};

// Vertex structure
struct Vertex {
    glm::vec3 pos;
    glm::vec3 normal;
    glm::vec2 texCoord;
};

// Forward declarations
struct VulkanDevice;
struct VulkanBuffer;
struct VulkanImage;
struct VulkanPipeline;

// Vulkan headless rendering backend
class VulkanBackend : public RenderBackend {
public:
    VulkanBackend();
    ~VulkanBackend();
    
    // RenderBackend interface implementation
    void renderScene(const Scene& scene, int width, int height, 
                    const Vec3& camera_pos, const Vec3& camera_dir, 
                    const Vec3& camera_up, double fov,
                    std::vector<std::vector<ConsoleCell>>& output_buffer) override;
    
    bool initialize() override;
    void cleanup() override;
    RenderBackendType getType() const override { return RenderBackendType::VULKAN_HEADLESS; }
    bool isAvailable() const override;
    
private:
    // Vulkan core objects
    VkInstance instance = VK_NULL_HANDLE;
    VkPhysicalDevice physical_device = VK_NULL_HANDLE;
    VkDevice device = VK_NULL_HANDLE;
    VkQueue graphics_queue = VK_NULL_HANDLE;
    VkCommandPool command_pool = VK_NULL_HANDLE;
    
    // Render targets and framebuffers
    std::unique_ptr<VulkanImage> color_image;
    std::unique_ptr<VulkanImage> depth_image;
    VkFramebuffer framebuffer = VK_NULL_HANDLE;
    VkRenderPass render_pass = VK_NULL_HANDLE;
    
    // Pipeline and shaders
    std::unique_ptr<VulkanPipeline> graphics_pipeline;
    
    // Buffers for scene data
    std::unique_ptr<VulkanBuffer> vertex_buffer;
    std::unique_ptr<VulkanBuffer> index_buffer;
    std::unique_ptr<VulkanBuffer> uniform_buffer;
    std::unique_ptr<VulkanBuffer> staging_buffer; // For CPU readback
    
    // Synchronization
    VkSemaphore render_complete_semaphore = VK_NULL_HANDLE;
    VkFence render_fence = VK_NULL_HANDLE;
    
    // Current render dimensions
    int current_width = 0;
    int current_height = 0;
    
    // Initialization methods
    bool createInstance();
    bool selectPhysicalDevice();
    bool createLogicalDevice();
    bool createCommandPool();
    bool createRenderPass();
    bool createFramebuffer(int width, int height);
    bool createSynchronization();
    
    // Resource management
    bool createBuffers();
    bool updateSceneData(const Scene& scene);
    bool updateUniformData(const Vec3& camera_pos, const Vec3& camera_dir, 
                          const Vec3& camera_up, double fov, int width, int height);
    
    // Rendering methods
    bool renderFrame();
    bool readbackFramebuffer(std::vector<std::vector<ConsoleCell>>& output_buffer);
    
    // Utility methods
    uint32_t findMemoryType(uint32_t type_filter, VkMemoryPropertyFlags properties);
    bool checkValidationLayerSupport();
    std::vector<const char*> getRequiredExtensions();

    // Shader utilities
    std::vector<char> readFile(const std::string& filename);
    VkShaderModule createShaderModule(const std::vector<char>& code);
    
    // Debug and validation
    VkDebugUtilsMessengerEXT debug_messenger = VK_NULL_HANDLE;
    bool enableValidationLayers = false;
    
    static VKAPI_ATTR VkBool32 VKAPI_CALL debugCallback(
        VkDebugUtilsMessageSeverityFlagBitsEXT messageSeverity,
        VkDebugUtilsMessageTypeFlagsEXT messageType,
        const VkDebugUtilsMessengerCallbackDataEXT* pCallbackData,
        void* pUserData);
};

// Helper structures for Vulkan resource management
struct VulkanDevice {
    VkDevice device;
    VkPhysicalDevice physical_device;
    uint32_t graphics_family_index;
    VkQueue graphics_queue;
};

struct VulkanBuffer {
    VkBuffer buffer = VK_NULL_HANDLE;
    VkDeviceMemory memory = VK_NULL_HANDLE;
    VkDeviceSize size = 0;
    void* mapped_data = nullptr;
    
    bool create(VkDevice device, VkPhysicalDevice physical_device, 
               VkDeviceSize buffer_size, VkBufferUsageFlags usage, 
               VkMemoryPropertyFlags properties);
    void destroy(VkDevice device);
    bool map(VkDevice device);
    void unmap(VkDevice device);
};

struct VulkanImage {
    VkImage image = VK_NULL_HANDLE;
    VkDeviceMemory memory = VK_NULL_HANDLE;
    VkImageView view = VK_NULL_HANDLE;
    VkFormat format;
    uint32_t width, height;
    
    bool create(VkDevice device, VkPhysicalDevice physical_device,
               uint32_t img_width, uint32_t img_height, VkFormat img_format,
               VkImageUsageFlags usage, VkMemoryPropertyFlags properties);
    void destroy(VkDevice device);
    bool createImageView(VkDevice device, VkImageAspectFlags aspect_flags);
};

struct VulkanPipeline {
    VkPipeline pipeline = VK_NULL_HANDLE;
    VkPipelineLayout layout = VK_NULL_HANDLE;
    VkDescriptorSetLayout descriptor_set_layout = VK_NULL_HANDLE;
    VkDescriptorPool descriptor_pool = VK_NULL_HANDLE;
    VkDescriptorSet descriptor_set = VK_NULL_HANDLE;
    
    bool create(VkDevice device, VkRenderPass render_pass);
    void destroy(VkDevice device);
};

#endif // VULKAN_BACKEND_H
