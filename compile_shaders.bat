@echo off
echo Compiling shaders...

set VULKAN_SDK=E:\VulkanSDK
set GLSLC=%VULKAN_SDK%\Bin\glslc.exe

if not exist "%GLSLC%" (
    echo Error: glslc.exe not found at %GLSLC%
    echo Please check your Vulkan SDK installation
    pause
    exit /b 1
)

if not exist "src\shaders\compiled" mkdir "src\shaders\compiled"

echo Compiling vertex shader...
"%GLSLC%" src\shaders\vertex.glsl -o src\shaders\compiled\vertex.spv
if %errorlevel% neq 0 (
    echo Error compiling vertex shader
    pause
    exit /b 1
)

echo Compiling fragment shader...
"%GLSLC%" src\shaders\fragment.glsl -o src\shaders\compiled\fragment.spv
if %errorlevel% neq 0 (
    echo Error compiling fragment shader
    pause
    exit /b 1
)

echo Shaders compiled successfully!
