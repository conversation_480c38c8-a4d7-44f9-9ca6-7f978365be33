#ifndef VULKAN_RENDERER_H
#define VULKAN_RENDERER_H

#include <vulkan/vulkan.h>
#include <vector>
#include <string>
#include <memory>
#include <future>
#include <random>
#include <chrono>

#include "console.h"
#include "scene.h"
#include "vector.h"
#include "ray.h"
#include "ui.h"
#include "console_utils.h"

const int TRANSITION_DURATION_FRAMES = 5;

// Vulkan buffer wrapper
struct VulkanBuffer {
    VkBuffer buffer = VK_NULL_HANDLE;
    VkDeviceMemory memory = VK_NULL_HANDLE;
    VkDeviceSize size = 0;
    void* mapped = nullptr;
};

// Vulkan image wrapper
struct VulkanImage {
    VkImage image = VK_NULL_HANDLE;
    VkDeviceMemory memory = VK_NULL_HANDLE;
    VkImageView view = VK_NULL_HANDLE;
    VkFormat format = VK_FORMAT_UNDEFINED;
    uint32_t width = 0;
    uint32_t height = 0;
};

// Camera uniform buffer object
struct CameraUBO {
    alignas(16) Vec3 position;
    alignas(16) Vec3 direction;
    alignas(16) Vec3 up;
    alignas(16) Vec3 right;
    float fov;
    float aspect_ratio;
    int width;
    int height;
};

// GPU material structure
struct GPUMaterial {
    alignas(16) Vec3 color;
    float ambient;
    float diffuse;
    float specular;
    float shininess;
    int texture_id;
    int symbol; // Changed from char to int for alignment
    int padding[2]; // Align to 16 bytes
};

// GPU triangle structure
struct GPUTriangle {
    alignas(16) Vec3 v0, v1, v2;
    alignas(16) Vec3 normal;
    alignas(8) Vec2 uv0, uv1, uv2;
    int material_id;
    int padding[3]; // Align to 16 bytes
};

// GPU light structure
struct GPULight {
    alignas(16) Vec3 position;
    alignas(16) Vec3 color;
    float intensity;
    float padding[3]; // Align to 16 bytes
};

// GPU BVH node structure
struct GPUBVHNode {
    alignas(16) Vec3 aabb_min;
    alignas(16) Vec3 aabb_max;
    int left_child;
    int right_child;
    int triangle_start;
    int triangle_count;
};

class VulkanRenderer {
public:
    VulkanRenderer(UI& ui_layer_ref);
    ~VulkanRenderer();

    // Main rendering function
    void render(const Scene& scene, int width, int height, const Vec3& camera_pos,
                const Vec3& camera_dir, const Vec3& camera_up, double fov, double fps,
                bool render_ui = true, bool is_loading = false);

    // Methods to manage render state
    void toggleNormalsVisualization();
    bool isNormalsVisualizationEnabled() const { return show_normals_mode; }
    void startTransition();
    bool isInTransition() const { return render_mode_transition_frames > 0; }
    void updateTransition();

    // Method to update animation states
    void updateAnimations(double deltaTime, double time_speed_multiplier, const Vec3& cloud_velocity);

    // Methods to get current state
    bool getShowNormalsMode() const { return show_normals_mode; }
    bool getBackgroundColorMode() const { return use_background_color_mode; }
    int getTransitionFrames() const { return render_mode_transition_frames; }

private:
    // Vulkan initialization
    bool initializeVulkan();
    void cleanupVulkan();
    
    // Device and queue management
    bool createInstance();
    bool selectPhysicalDevice();
    bool createLogicalDevice();
    
    // Memory management
    uint32_t findMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties);
    bool createBuffer(VkDeviceSize size, VkBufferUsageFlags usage, 
                     VkMemoryPropertyFlags properties, VulkanBuffer& buffer);
    void destroyBuffer(VulkanBuffer& buffer);
    
    // Image management
    bool createImage(uint32_t width, uint32_t height, VkFormat format,
                    VkImageUsageFlags usage, VkMemoryPropertyFlags properties,
                    VulkanImage& image);
    void destroyImage(VulkanImage& image);
    
    // Compute pipeline
    bool createComputePipeline();
    bool createDescriptorSets();
    void updateDescriptorSets();
    
    // Scene data upload
    void uploadSceneData(const Scene& scene);
    void uploadCameraData(const Vec3& camera_pos, const Vec3& camera_dir, 
                         const Vec3& camera_up, double fov, int width, int height);
    
    // Rendering
    void dispatch(int width, int height);
    void readbackImage(int width, int height);
    
    // Output conversion
    void convertToConsoleBuffer(int width, int height);
    std::vector<UpdateCommand> generateUpdateCommands(int width, int height);
    void outputUpdateCommands(const std::vector<UpdateCommand>& update_commands, int height);
    char colorToAsciiChar(const RGBColor& color);

    // Vulkan objects
    VkInstance instance = VK_NULL_HANDLE;
    VkPhysicalDevice physical_device = VK_NULL_HANDLE;
    VkDevice device = VK_NULL_HANDLE;
    VkQueue compute_queue = VK_NULL_HANDLE;
    uint32_t compute_queue_family = UINT32_MAX;
    
    VkCommandPool command_pool = VK_NULL_HANDLE;
    VkCommandBuffer command_buffer = VK_NULL_HANDLE;
    
    VkDescriptorSetLayout descriptor_set_layout = VK_NULL_HANDLE;
    VkDescriptorPool descriptor_pool = VK_NULL_HANDLE;
    VkDescriptorSet descriptor_set = VK_NULL_HANDLE;
    
    VkPipelineLayout pipeline_layout = VK_NULL_HANDLE;
    VkPipeline compute_pipeline = VK_NULL_HANDLE;
    
    // Buffers and images
    VulkanImage output_image;
    VulkanBuffer staging_buffer;
    VulkanBuffer camera_buffer;
    VulkanBuffer material_buffer;
    VulkanBuffer triangle_buffer;
    VulkanBuffer light_buffer;
    VulkanBuffer bvh_buffer;
    VulkanBuffer triangle_index_buffer;
    
    // Persistent console buffers
    std::vector<std::vector<ConsoleCell>> previous_buffer;
    std::vector<std::vector<ConsoleCell>> current_buffer;
    
    // Render state flags
    bool first_frame = true;
    bool use_background_color_mode = false;
    bool show_normals_mode = false;
    bool vulkan_initialized = false;
    
    // UI reference
    UI& ui_layer;
    
    // Render mode transition effect state
    int render_mode_transition_frames = 0;
    
    // Animation states
    Vec3 cloud_animation_offset = Vec3(0.0, 0.0, 0.0);
    double current_time = M_PI;
    
    // Random number generators for transition effect
    static std::mt19937 rng;
    static std::uniform_int_distribution<int> char_dist;
    static std::uniform_int_distribution<int> gray_dist;
};

#endif // VULKAN_RENDERER_H
