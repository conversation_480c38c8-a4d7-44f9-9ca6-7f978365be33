{
    cmdlines = {
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake ",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua"]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua"]],
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake f -p windows -a x64 -m release",
        "xmake ",
        "xmake ",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake ",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua"]],
        "xmake f -p windows -a x64 -m release",
        "xmake ",
        "xmake check",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake run",
        "xmake clean",
        "xmake ",
        "xmake run",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake check",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        "xmake build",
        "xmake build",
        "xmake build",
        "xmake build",
        "xmake build",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\update_intellisense.lua .vscode clangd]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        "xmake build",
        "xmake check"
    }
}