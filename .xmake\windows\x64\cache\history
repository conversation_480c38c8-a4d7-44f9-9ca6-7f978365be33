{
    cmdlines = {
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake f -p windows -a x64 -m release",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake ",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua"]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua"]],
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake "
    }
}