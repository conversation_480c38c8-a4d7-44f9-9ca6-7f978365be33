#include "vulkan_backend.h"
#include "ui.h"
#include <iostream>
#include <vector>
#include <set>
#include <algorithm>
#include <cstring>
#include <fstream>
#include <array>

VulkanBackend::VulkanBackend() {
#ifdef _DEBUG
    enableValidationLayers = true;
#endif
}

VulkanBackend::~VulkanBackend() {
    cleanup();
}

bool VulkanBackend::isAvailable() const {
    // Check if Vulkan is available by attempting to load the library
    // This is a simple check - in a real implementation you might want to cache this result
    VkInstance test_instance = VK_NULL_HANDLE;
    
    VkApplicationInfo app_info{};
    app_info.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
    app_info.pApplicationName = "RTerminal Vulkan Test";
    app_info.applicationVersion = VK_MAKE_VERSION(1, 0, 0);
    app_info.pEngineName = "RTerminal Engine";
    app_info.engineVersion = VK_MAKE_VERSION(1, 0, 0);
    app_info.apiVersion = VK_API_VERSION_1_0;

    VkInstanceCreateInfo create_info{};
    create_info.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
    create_info.pApplicationInfo = &app_info;

    // Try to create a minimal instance
    VkResult result = vkCreateInstance(&create_info, nullptr, &test_instance);
    
    if (test_instance != VK_NULL_HANDLE) {
        vkDestroyInstance(test_instance, nullptr);
    }
    
    return result == VK_SUCCESS;
}

bool VulkanBackend::initialize() {
    if (!createInstance()) {
        std::cerr << "Failed to create Vulkan instance" << std::endl;
        return false;
    }

    if (!selectPhysicalDevice()) {
        std::cerr << "Failed to find suitable physical device" << std::endl;
        return false;
    }

    if (!createLogicalDevice()) {
        std::cerr << "Failed to create logical device" << std::endl;
        return false;
    }

    if (!createCommandPool()) {
        std::cerr << "Failed to create command pool" << std::endl;
        return false;
    }

    if (!createRenderPass()) {
        std::cerr << "Failed to create render pass" << std::endl;
        return false;
    }

    if (!createSynchronization()) {
        std::cerr << "Failed to create synchronization objects" << std::endl;
        return false;
    }

    // Create graphics pipeline
    graphics_pipeline = std::make_unique<VulkanPipeline>();
    if (!graphics_pipeline->create(device, render_pass)) {
        std::cerr << "Failed to create graphics pipeline" << std::endl;
        return false;
    }

    return true;
}

bool VulkanBackend::createInstance() {
    VkApplicationInfo app_info{};
    app_info.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
    app_info.pApplicationName = "RTerminal";
    app_info.applicationVersion = VK_MAKE_VERSION(1, 0, 0);
    app_info.pEngineName = "RTerminal Engine";
    app_info.engineVersion = VK_MAKE_VERSION(1, 0, 0);
    app_info.apiVersion = VK_API_VERSION_1_0;

    VkInstanceCreateInfo create_info{};
    create_info.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
    create_info.pApplicationInfo = &app_info;

    auto extensions = getRequiredExtensions();
    create_info.enabledExtensionCount = static_cast<uint32_t>(extensions.size());
    create_info.ppEnabledExtensionNames = extensions.data();

    VkDebugUtilsMessengerCreateInfoEXT debug_create_info{};
    if (enableValidationLayers) {
        const std::vector<const char*> validation_layers = {
            "VK_LAYER_KHRONOS_validation"
        };
        
        if (checkValidationLayerSupport()) {
            create_info.enabledLayerCount = static_cast<uint32_t>(validation_layers.size());
            create_info.ppEnabledLayerNames = validation_layers.data();

            debug_create_info.sType = VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT;
            debug_create_info.messageSeverity = VK_DEBUG_UTILS_MESSAGE_SEVERITY_VERBOSE_BIT_EXT | 
                                               VK_DEBUG_UTILS_MESSAGE_SEVERITY_WARNING_BIT_EXT | 
                                               VK_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT;
            debug_create_info.messageType = VK_DEBUG_UTILS_MESSAGE_TYPE_GENERAL_BIT_EXT | 
                                          VK_DEBUG_UTILS_MESSAGE_TYPE_VALIDATION_BIT_EXT | 
                                          VK_DEBUG_UTILS_MESSAGE_TYPE_PERFORMANCE_BIT_EXT;
            debug_create_info.pfnUserCallback = debugCallback;
            create_info.pNext = (VkDebugUtilsMessengerCreateInfoEXT*)&debug_create_info;
        } else {
            enableValidationLayers = false;
        }
    }

    VkResult result = vkCreateInstance(&create_info, nullptr, &instance);
    return result == VK_SUCCESS;
}

bool VulkanBackend::selectPhysicalDevice() {
    uint32_t device_count = 0;
    vkEnumeratePhysicalDevices(instance, &device_count, nullptr);

    if (device_count == 0) {
        return false;
    }

    std::vector<VkPhysicalDevice> devices(device_count);
    vkEnumeratePhysicalDevices(instance, &device_count, devices.data());

    // For headless rendering, we just need a device with graphics capabilities
    for (const auto& device : devices) {
        VkPhysicalDeviceProperties device_properties;
        vkGetPhysicalDeviceProperties(device, &device_properties);

        VkPhysicalDeviceFeatures device_features;
        vkGetPhysicalDeviceFeatures(device, &device_features);

        // Find graphics queue family
        uint32_t queue_family_count = 0;
        vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count, nullptr);

        std::vector<VkQueueFamilyProperties> queue_families(queue_family_count);
        vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count, queue_families.data());

        for (uint32_t i = 0; i < queue_families.size(); i++) {
            if (queue_families[i].queueFlags & VK_QUEUE_GRAPHICS_BIT) {
                physical_device = device;
                return true;
            }
        }
    }

    return false;
}

bool VulkanBackend::createLogicalDevice() {
    // Find graphics queue family
    uint32_t queue_family_count = 0;
    vkGetPhysicalDeviceQueueFamilyProperties(physical_device, &queue_family_count, nullptr);

    std::vector<VkQueueFamilyProperties> queue_families(queue_family_count);
    vkGetPhysicalDeviceQueueFamilyProperties(physical_device, &queue_family_count, queue_families.data());

    uint32_t graphics_family = UINT32_MAX;
    for (uint32_t i = 0; i < queue_families.size(); i++) {
        if (queue_families[i].queueFlags & VK_QUEUE_GRAPHICS_BIT) {
            graphics_family = i;
            break;
        }
    }

    if (graphics_family == UINT32_MAX) {
        return false;
    }

    VkDeviceQueueCreateInfo queue_create_info{};
    queue_create_info.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
    queue_create_info.queueFamilyIndex = graphics_family;
    queue_create_info.queueCount = 1;

    float queue_priority = 1.0f;
    queue_create_info.pQueuePriorities = &queue_priority;

    VkPhysicalDeviceFeatures device_features{};

    VkDeviceCreateInfo create_info{};
    create_info.sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO;
    create_info.pQueueCreateInfos = &queue_create_info;
    create_info.queueCreateInfoCount = 1;
    create_info.pEnabledFeatures = &device_features;
    create_info.enabledExtensionCount = 0;

    if (enableValidationLayers) {
        const std::vector<const char*> validation_layers = {
            "VK_LAYER_KHRONOS_validation"
        };
        create_info.enabledLayerCount = static_cast<uint32_t>(validation_layers.size());
        create_info.ppEnabledLayerNames = validation_layers.data();
    } else {
        create_info.enabledLayerCount = 0;
    }

    VkResult result = vkCreateDevice(physical_device, &create_info, nullptr, &device);
    if (result != VK_SUCCESS) {
        return false;
    }

    vkGetDeviceQueue(device, graphics_family, 0, &graphics_queue);
    return true;
}

bool VulkanBackend::createCommandPool() {
    // Find graphics queue family again
    uint32_t queue_family_count = 0;
    vkGetPhysicalDeviceQueueFamilyProperties(physical_device, &queue_family_count, nullptr);

    std::vector<VkQueueFamilyProperties> queue_families(queue_family_count);
    vkGetPhysicalDeviceQueueFamilyProperties(physical_device, &queue_family_count, queue_families.data());

    uint32_t graphics_family = UINT32_MAX;
    for (uint32_t i = 0; i < queue_families.size(); i++) {
        if (queue_families[i].queueFlags & VK_QUEUE_GRAPHICS_BIT) {
            graphics_family = i;
            break;
        }
    }

    VkCommandPoolCreateInfo pool_info{};
    pool_info.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
    pool_info.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
    pool_info.queueFamilyIndex = graphics_family;

    VkResult result = vkCreateCommandPool(device, &pool_info, nullptr, &command_pool);
    return result == VK_SUCCESS;
}

bool VulkanBackend::createRenderPass() {
    VkAttachmentDescription color_attachment{};
    color_attachment.format = VK_FORMAT_R8G8B8A8_UNORM;
    color_attachment.samples = VK_SAMPLE_COUNT_1_BIT;
    color_attachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
    color_attachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
    color_attachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
    color_attachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
    color_attachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    color_attachment.finalLayout = VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL;

    VkAttachmentDescription depth_attachment{};
    depth_attachment.format = VK_FORMAT_D32_SFLOAT;
    depth_attachment.samples = VK_SAMPLE_COUNT_1_BIT;
    depth_attachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
    depth_attachment.storeOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
    depth_attachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
    depth_attachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
    depth_attachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    depth_attachment.finalLayout = VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;

    VkAttachmentReference color_attachment_ref{};
    color_attachment_ref.attachment = 0;
    color_attachment_ref.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;

    VkAttachmentReference depth_attachment_ref{};
    depth_attachment_ref.attachment = 1;
    depth_attachment_ref.layout = VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;

    VkSubpassDescription subpass{};
    subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
    subpass.colorAttachmentCount = 1;
    subpass.pColorAttachments = &color_attachment_ref;
    subpass.pDepthStencilAttachment = &depth_attachment_ref;

    VkSubpassDependency dependency{};
    dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
    dependency.dstSubpass = 0;
    dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT | VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    dependency.srcAccessMask = 0;
    dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT | VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT | VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT;

    std::array<VkAttachmentDescription, 2> attachments = {color_attachment, depth_attachment};
    VkRenderPassCreateInfo render_pass_info{};
    render_pass_info.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
    render_pass_info.attachmentCount = static_cast<uint32_t>(attachments.size());
    render_pass_info.pAttachments = attachments.data();
    render_pass_info.subpassCount = 1;
    render_pass_info.pSubpasses = &subpass;
    render_pass_info.dependencyCount = 1;
    render_pass_info.pDependencies = &dependency;

    VkResult result = vkCreateRenderPass(device, &render_pass_info, nullptr, &render_pass);
    return result == VK_SUCCESS;
}

bool VulkanBackend::createSynchronization() {
    VkSemaphoreCreateInfo semaphore_info{};
    semaphore_info.sType = VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO;

    VkFenceCreateInfo fence_info{};
    fence_info.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
    fence_info.flags = VK_FENCE_CREATE_SIGNALED_BIT;

    VkResult result1 = vkCreateSemaphore(device, &semaphore_info, nullptr, &render_complete_semaphore);
    VkResult result2 = vkCreateFence(device, &fence_info, nullptr, &render_fence);

    return result1 == VK_SUCCESS && result2 == VK_SUCCESS;
}

void VulkanBackend::cleanup() {
    if (device != VK_NULL_HANDLE) {
        vkDeviceWaitIdle(device);

        if (render_complete_semaphore != VK_NULL_HANDLE) {
            vkDestroySemaphore(device, render_complete_semaphore, nullptr);
            render_complete_semaphore = VK_NULL_HANDLE;
        }

        if (render_fence != VK_NULL_HANDLE) {
            vkDestroyFence(device, render_fence, nullptr);
            render_fence = VK_NULL_HANDLE;
        }

        if (framebuffer != VK_NULL_HANDLE) {
            vkDestroyFramebuffer(device, framebuffer, nullptr);
            framebuffer = VK_NULL_HANDLE;
        }

        if (render_pass != VK_NULL_HANDLE) {
            vkDestroyRenderPass(device, render_pass, nullptr);
            render_pass = VK_NULL_HANDLE;
        }

        if (graphics_pipeline) {
            graphics_pipeline->destroy(device);
            graphics_pipeline.reset();
        }

        if (vertex_buffer) {
            vertex_buffer->destroy(device);
            vertex_buffer.reset();
        }

        if (index_buffer) {
            index_buffer->destroy(device);
            index_buffer.reset();
        }

        if (uniform_buffer) {
            uniform_buffer->destroy(device);
            uniform_buffer.reset();
        }

        if (staging_buffer) {
            staging_buffer->destroy(device);
            staging_buffer.reset();
        }

        if (color_image) {
            color_image->destroy(device);
            color_image.reset();
        }

        if (depth_image) {
            depth_image->destroy(device);
            depth_image.reset();
        }

        if (command_pool != VK_NULL_HANDLE) {
            vkDestroyCommandPool(device, command_pool, nullptr);
            command_pool = VK_NULL_HANDLE;
        }

        vkDestroyDevice(device, nullptr);
        device = VK_NULL_HANDLE;
    }

    if (debug_messenger != VK_NULL_HANDLE && instance != VK_NULL_HANDLE) {
        auto func = (PFN_vkDestroyDebugUtilsMessengerEXT)vkGetInstanceProcAddr(instance, "vkDestroyDebugUtilsMessengerEXT");
        if (func != nullptr) {
            func(instance, debug_messenger, nullptr);
        }
        debug_messenger = VK_NULL_HANDLE;
    }

    if (instance != VK_NULL_HANDLE) {
        vkDestroyInstance(instance, nullptr);
        instance = VK_NULL_HANDLE;
    }
}

std::vector<const char*> VulkanBackend::getRequiredExtensions() {
    std::vector<const char*> extensions;

    if (enableValidationLayers) {
        extensions.push_back(VK_EXT_DEBUG_UTILS_EXTENSION_NAME);
    }

    return extensions;
}

bool VulkanBackend::checkValidationLayerSupport() {
    uint32_t layer_count;
    vkEnumerateInstanceLayerProperties(&layer_count, nullptr);

    std::vector<VkLayerProperties> available_layers(layer_count);
    vkEnumerateInstanceLayerProperties(&layer_count, available_layers.data());

    const std::vector<const char*> validation_layers = {
        "VK_LAYER_KHRONOS_validation"
    };

    for (const char* layer_name : validation_layers) {
        bool layer_found = false;

        for (const auto& layer_properties : available_layers) {
            if (strcmp(layer_name, layer_properties.layerName) == 0) {
                layer_found = true;
                break;
            }
        }

        if (!layer_found) {
            return false;
        }
    }

    return true;
}

uint32_t VulkanBackend::findMemoryType(uint32_t type_filter, VkMemoryPropertyFlags properties) {
    VkPhysicalDeviceMemoryProperties mem_properties;
    vkGetPhysicalDeviceMemoryProperties(physical_device, &mem_properties);

    for (uint32_t i = 0; i < mem_properties.memoryTypeCount; i++) {
        if ((type_filter & (1 << i)) && (mem_properties.memoryTypes[i].propertyFlags & properties) == properties) {
            return i;
        }
    }

    return UINT32_MAX;
}

VKAPI_ATTR VkBool32 VKAPI_CALL VulkanBackend::debugCallback(
    VkDebugUtilsMessageSeverityFlagBitsEXT messageSeverity,
    VkDebugUtilsMessageTypeFlagsEXT messageType,
    const VkDebugUtilsMessengerCallbackDataEXT* pCallbackData,
    void* pUserData) {

    std::cerr << "Vulkan validation layer: " << pCallbackData->pMessage << std::endl;
    return VK_FALSE;
}

void VulkanBackend::renderScene(const Scene& scene, int width, int height,
                               const Vec3& camera_pos, const Vec3& camera_dir,
                               const Vec3& camera_up, double fov,
                               std::vector<std::vector<ConsoleCell>>& output_buffer) {
    // Ensure output buffer is properly sized
    if (output_buffer.size() != static_cast<size_t>(height) ||
        (height > 0 && output_buffer[0].size() != static_cast<size_t>(width))) {
        output_buffer.assign(height, std::vector<ConsoleCell>(width));
    }

    // Check if we need to recreate framebuffer for new dimensions
    if (width != current_width || height != current_height) {
        if (!createFramebuffer(width, height)) {
            // Fall back to test pattern on error
            for (int y = 0; y < height; ++y) {
                for (int x = 0; x < width; ++x) {
                    ConsoleCell& cell = output_buffer[y][x];
                    cell.character = '!';
                    cell.color = RGBColor{255, 0, 0}; // Red for error
                }
            }
            return;
        }

        // Recreate buffers for new size
        if (!createBuffers()) {
            return;
        }
    }

    // Update uniform data
    if (!updateUniformData(camera_pos, camera_dir, camera_up, fov, width, height)) {
        return;
    }

    // Update scene data (for now just creates a test triangle)
    static bool scene_uploaded = false;
    if (!scene_uploaded) {
        updateSceneData(scene);

        // Update descriptor set with uniform buffer
        if (graphics_pipeline && graphics_pipeline->descriptor_set != VK_NULL_HANDLE && uniform_buffer) {
            VkDescriptorBufferInfo bufferInfo{};
            bufferInfo.buffer = uniform_buffer->buffer;
            bufferInfo.offset = 0;
            bufferInfo.range = sizeof(UniformBufferObject);

            VkWriteDescriptorSet descriptorWrite{};
            descriptorWrite.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
            descriptorWrite.dstSet = graphics_pipeline->descriptor_set;
            descriptorWrite.dstBinding = 0;
            descriptorWrite.dstArrayElement = 0;
            descriptorWrite.descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
            descriptorWrite.descriptorCount = 1;
            descriptorWrite.pBufferInfo = &bufferInfo;

            vkUpdateDescriptorSets(device, 1, &descriptorWrite, 0, nullptr);
        }

        scene_uploaded = true;
    }

    // Render frame (placeholder for now)
    if (!renderFrame()) {
        return;
    }

    // Read back framebuffer and convert to ConsoleCell format
    if (!readbackFramebuffer(output_buffer)) {
        // Fill with Vulkan test pattern to show it's working
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                ConsoleCell& cell = output_buffer[y][x];
                cell.character = (x + y) % 2 == 0 ? 'V' : 'K'; // VK pattern
                cell.color = RGBColor{100, 255, 100}; // Green tint to indicate Vulkan is active
            }
        }
    }
}

bool VulkanBackend::createFramebuffer(int width, int height) {
    current_width = width;
    current_height = height;

    // Create color image
    color_image = std::make_unique<VulkanImage>();
    if (!color_image->create(device, physical_device, width, height, VK_FORMAT_R8G8B8A8_UNORM,
                           VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT | VK_IMAGE_USAGE_TRANSFER_SRC_BIT,
                           VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT)) {
        return false;
    }

    if (!color_image->createImageView(device, VK_IMAGE_ASPECT_COLOR_BIT)) {
        return false;
    }

    // Create depth image
    depth_image = std::make_unique<VulkanImage>();
    if (!depth_image->create(device, physical_device, width, height, VK_FORMAT_D32_SFLOAT,
                           VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT,
                           VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT)) {
        return false;
    }

    if (!depth_image->createImageView(device, VK_IMAGE_ASPECT_DEPTH_BIT)) {
        return false;
    }

    // Create framebuffer
    std::array<VkImageView, 2> attachments = {
        color_image->view,
        depth_image->view
    };

    VkFramebufferCreateInfo framebufferInfo{};
    framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
    framebufferInfo.renderPass = render_pass;
    framebufferInfo.attachmentCount = static_cast<uint32_t>(attachments.size());
    framebufferInfo.pAttachments = attachments.data();
    framebufferInfo.width = width;
    framebufferInfo.height = height;
    framebufferInfo.layers = 1;

    VkResult result = vkCreateFramebuffer(device, &framebufferInfo, nullptr, &framebuffer);
    return result == VK_SUCCESS;
}

bool VulkanBackend::createBuffers() {
    // Create uniform buffer
    VkDeviceSize uniformBufferSize = sizeof(UniformBufferObject);
    uniform_buffer = std::make_unique<VulkanBuffer>();
    if (!uniform_buffer->create(device, physical_device, uniformBufferSize,
                              VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
                              VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT)) {
        return false;
    }

    // Map uniform buffer for persistent mapping
    if (!uniform_buffer->map(device)) {
        return false;
    }

    // Create staging buffer for readback
    VkDeviceSize stagingBufferSize = current_width * current_height * 4; // RGBA
    staging_buffer = std::make_unique<VulkanBuffer>();
    if (!staging_buffer->create(device, physical_device, stagingBufferSize,
                              VK_BUFFER_USAGE_TRANSFER_DST_BIT,
                              VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT)) {
        return false;
    }

    return true;
}

bool VulkanBackend::updateSceneData(const Scene& scene) {
    // For now, create a simple test triangle
    std::vector<Vertex> vertices = {
        {{-0.5f, -0.5f, 0.0f}, {0.0f, 0.0f, 1.0f}, {0.0f, 0.0f}},
        {{ 0.5f, -0.5f, 0.0f}, {0.0f, 0.0f, 1.0f}, {1.0f, 0.0f}},
        {{ 0.0f,  0.5f, 0.0f}, {0.0f, 0.0f, 1.0f}, {0.5f, 1.0f}}
    };

    std::vector<uint16_t> indices = {0, 1, 2};

    // Create vertex buffer
    VkDeviceSize vertexBufferSize = sizeof(vertices[0]) * vertices.size();
    vertex_buffer = std::make_unique<VulkanBuffer>();
    if (!vertex_buffer->create(device, physical_device, vertexBufferSize,
                             VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
                             VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT)) {
        return false;
    }

    // Upload vertex data
    if (vertex_buffer->map(device)) {
        memcpy(vertex_buffer->mapped_data, vertices.data(), vertexBufferSize);
        vertex_buffer->unmap(device);
    }

    // Create index buffer
    VkDeviceSize indexBufferSize = sizeof(indices[0]) * indices.size();
    index_buffer = std::make_unique<VulkanBuffer>();
    if (!index_buffer->create(device, physical_device, indexBufferSize,
                            VK_BUFFER_USAGE_INDEX_BUFFER_BIT,
                            VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT)) {
        return false;
    }

    // Upload index data
    if (index_buffer->map(device)) {
        memcpy(index_buffer->mapped_data, indices.data(), indexBufferSize);
        index_buffer->unmap(device);
    }

    return true;
}

bool VulkanBackend::updateUniformData(const Vec3& camera_pos, const Vec3& camera_dir,
                                     const Vec3& camera_up, double fov, int width, int height) {
    if (!uniform_buffer || !uniform_buffer->mapped_data) {
        return false;
    }

    UniformBufferObject ubo{};

    // Model matrix (identity for now)
    ubo.model = glm::mat4(1.0f);

    // View matrix
    glm::vec3 eye(camera_pos.x, camera_pos.y, camera_pos.z);
    glm::vec3 center = eye + glm::vec3(camera_dir.x, camera_dir.y, camera_dir.z);
    glm::vec3 up(camera_up.x, camera_up.y, camera_up.z);
    ubo.view = glm::lookAt(eye, center, up);

    // Projection matrix
    float aspect = static_cast<float>(width) / static_cast<float>(height);
    ubo.proj = glm::perspective(glm::radians(static_cast<float>(fov)), aspect, 0.1f, 100.0f);

    // Vulkan uses different clip space than OpenGL
    ubo.proj[1][1] *= -1;

    // Camera position
    ubo.cameraPos = glm::vec3(camera_pos.x, camera_pos.y, camera_pos.z);

    // Simple lighting setup
    ubo.lightPos = glm::vec3(5.0f, 5.0f, 5.0f);
    ubo.lightColor = glm::vec3(1.0f, 1.0f, 1.0f);
    ubo.lightIntensity = 1.0f;
    ubo.time = 0.0f; // TODO: Add time parameter

    // Copy to uniform buffer
    memcpy(uniform_buffer->mapped_data, &ubo, sizeof(ubo));

    return true;
}

bool VulkanBackend::renderFrame() {
    if (!framebuffer || !render_pass) {
        return false;
    }

    // Create command buffer for rendering
    VkCommandBufferAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
    allocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
    allocInfo.commandPool = command_pool;
    allocInfo.commandBufferCount = 1;

    VkCommandBuffer commandBuffer;
    vkAllocateCommandBuffers(device, &allocInfo, &commandBuffer);

    VkCommandBufferBeginInfo beginInfo{};
    beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    beginInfo.flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT;

    vkBeginCommandBuffer(commandBuffer, &beginInfo);

    // Begin render pass
    VkRenderPassBeginInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
    renderPassInfo.renderPass = render_pass;
    renderPassInfo.framebuffer = framebuffer;
    renderPassInfo.renderArea.offset = {0, 0};
    renderPassInfo.renderArea.extent = {static_cast<uint32_t>(current_width), static_cast<uint32_t>(current_height)};

    std::array<VkClearValue, 2> clearValues{};
    clearValues[0].color = {{0.0f, 0.0f, 0.2f, 1.0f}}; // Dark blue background
    clearValues[1].depthStencil = {1.0f, 0};

    renderPassInfo.clearValueCount = static_cast<uint32_t>(clearValues.size());
    renderPassInfo.pClearValues = clearValues.data();

    vkCmdBeginRenderPass(commandBuffer, &renderPassInfo, VK_SUBPASS_CONTENTS_INLINE);

    // Set viewport
    VkViewport viewport{};
    viewport.x = 0.0f;
    viewport.y = 0.0f;
    viewport.width = static_cast<float>(current_width);
    viewport.height = static_cast<float>(current_height);
    viewport.minDepth = 0.0f;
    viewport.maxDepth = 1.0f;
    vkCmdSetViewport(commandBuffer, 0, 1, &viewport);

    // Set scissor
    VkRect2D scissor{};
    scissor.offset = {0, 0};
    scissor.extent = {static_cast<uint32_t>(current_width), static_cast<uint32_t>(current_height)};
    vkCmdSetScissor(commandBuffer, 0, 1, &scissor);

    // Bind graphics pipeline
    if (graphics_pipeline && graphics_pipeline->pipeline != VK_NULL_HANDLE) {
        vkCmdBindPipeline(commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, graphics_pipeline->pipeline);

        // Bind vertex buffer
        if (vertex_buffer && vertex_buffer->buffer != VK_NULL_HANDLE) {
            VkBuffer vertexBuffers[] = {vertex_buffer->buffer};
            VkDeviceSize offsets[] = {0};
            vkCmdBindVertexBuffers(commandBuffer, 0, 1, vertexBuffers, offsets);
        }

        // Bind index buffer
        if (index_buffer && index_buffer->buffer != VK_NULL_HANDLE) {
            vkCmdBindIndexBuffer(commandBuffer, index_buffer->buffer, 0, VK_INDEX_TYPE_UINT16);
        }

        // Bind descriptor sets (uniform buffer)
        if (graphics_pipeline->descriptor_set != VK_NULL_HANDLE) {
            vkCmdBindDescriptorSets(commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                                  graphics_pipeline->layout, 0, 1, &graphics_pipeline->descriptor_set, 0, nullptr);
        }

        // Draw the triangle
        vkCmdDrawIndexed(commandBuffer, 3, 1, 0, 0, 0);
    }

    vkCmdEndRenderPass(commandBuffer);
    vkEndCommandBuffer(commandBuffer);

    // Submit command buffer
    VkSubmitInfo submitInfo{};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &commandBuffer;

    vkQueueSubmit(graphics_queue, 1, &submitInfo, VK_NULL_HANDLE);
    vkQueueWaitIdle(graphics_queue);

    // Clean up command buffer
    vkFreeCommandBuffers(device, command_pool, 1, &commandBuffer);

    return true;
}

bool VulkanBackend::readbackFramebuffer(std::vector<std::vector<ConsoleCell>>& output_buffer) {
    if (!color_image || !staging_buffer) {
        return false;
    }

    // Create command buffer for the copy operation
    VkCommandBufferAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
    allocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
    allocInfo.commandPool = command_pool;
    allocInfo.commandBufferCount = 1;

    VkCommandBuffer commandBuffer;
    vkAllocateCommandBuffers(device, &allocInfo, &commandBuffer);

    VkCommandBufferBeginInfo beginInfo{};
    beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    beginInfo.flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT;

    vkBeginCommandBuffer(commandBuffer, &beginInfo);

    // Transition image layout for transfer
    VkImageMemoryBarrier barrier{};
    barrier.sType = VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER;
    barrier.oldLayout = VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL;
    barrier.newLayout = VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL;
    barrier.srcQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.dstQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.image = color_image->image;
    barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    barrier.subresourceRange.baseMipLevel = 0;
    barrier.subresourceRange.levelCount = 1;
    barrier.subresourceRange.baseArrayLayer = 0;
    barrier.subresourceRange.layerCount = 1;
    barrier.srcAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
    barrier.dstAccessMask = VK_ACCESS_TRANSFER_READ_BIT;

    vkCmdPipelineBarrier(commandBuffer, VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT,
                        VK_PIPELINE_STAGE_TRANSFER_BIT, 0, 0, nullptr, 0, nullptr, 1, &barrier);

    // Copy image to buffer
    VkBufferImageCopy region{};
    region.bufferOffset = 0;
    region.bufferRowLength = 0;
    region.bufferImageHeight = 0;
    region.imageSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    region.imageSubresource.mipLevel = 0;
    region.imageSubresource.baseArrayLayer = 0;
    region.imageSubresource.layerCount = 1;
    region.imageOffset = {0, 0, 0};
    region.imageExtent = {static_cast<uint32_t>(current_width), static_cast<uint32_t>(current_height), 1};

    vkCmdCopyImageToBuffer(commandBuffer, color_image->image, VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
                          staging_buffer->buffer, 1, &region);

    vkEndCommandBuffer(commandBuffer);

    // Submit command buffer
    VkSubmitInfo submitInfo{};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &commandBuffer;

    vkQueueSubmit(graphics_queue, 1, &submitInfo, VK_NULL_HANDLE);
    vkQueueWaitIdle(graphics_queue);

    // Clean up command buffer
    vkFreeCommandBuffers(device, command_pool, 1, &commandBuffer);

    // Map staging buffer and read pixel data
    if (!staging_buffer->map(device)) {
        return false;
    }

    uint8_t* pixelData = static_cast<uint8_t*>(staging_buffer->mapped_data);

    // Convert pixel data to ConsoleCell format
    for (int y = 0; y < current_height; ++y) {
        for (int x = 0; x < current_width; ++x) {
            if (y < static_cast<int>(output_buffer.size()) && x < static_cast<int>(output_buffer[y].size())) {
                // Calculate pixel offset (RGBA format)
                int pixelOffset = (y * current_width + x) * 4;

                uint8_t r = pixelData[pixelOffset + 0];
                uint8_t g = pixelData[pixelOffset + 1];
                uint8_t b = pixelData[pixelOffset + 2];
                // uint8_t a = pixelData[pixelOffset + 3]; // Alpha not used

                ConsoleCell& cell = output_buffer[y][x];
                cell.color = RGBColor{static_cast<int>(r), static_cast<int>(g), static_cast<int>(b)};

                // Calculate luminance for ASCII character selection
                double luminance = 0.299 * r + 0.587 * g + 0.114 * b;

                // Simple ASCII character mapping based on luminance
                if (luminance > 200) cell.character = '#';
                else if (luminance > 150) cell.character = '*';
                else if (luminance > 100) cell.character = '+';
                else if (luminance > 50) cell.character = '.';
                else cell.character = ' ';

                cell.is_ui = false;
                cell.is_border = false;
            }
        }
    }

    staging_buffer->unmap(device);
    return true;
}

// Helper method to read shader file
std::vector<char> VulkanBackend::readFile(const std::string& filename) {
    std::ifstream file(filename, std::ios::ate | std::ios::binary);

    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file: " + filename);
    }

    size_t fileSize = (size_t)file.tellg();
    std::vector<char> buffer(fileSize);

    file.seekg(0);
    file.read(buffer.data(), fileSize);
    file.close();

    return buffer;
}

// Helper method to create shader module
VkShaderModule VulkanBackend::createShaderModule(const std::vector<char>& code) {
    VkShaderModuleCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = code.size();
    createInfo.pCode = reinterpret_cast<const uint32_t*>(code.data());

    VkShaderModule shaderModule;
    if (vkCreateShaderModule(device, &createInfo, nullptr, &shaderModule) != VK_SUCCESS) {
        throw std::runtime_error("Failed to create shader module");
    }

    return shaderModule;
}
