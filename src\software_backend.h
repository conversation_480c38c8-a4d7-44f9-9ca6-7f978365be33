#ifndef SOFTWARE_BACKEND_H
#define SOFTWARE_BACKEND_H

#include "renderer.h"
#include "console.h"
#include "scene.h"
#include "vector.h"
#include "ray.h"
#include "skybox.h"
#include "shading.h"
#include <vector>
#include <future>
#include <thread>

// Software rendering backend that implements the existing raytracing logic
class SoftwareBackend : public RenderBackend {
public:
    SoftwareBackend(bool& show_normals_mode_ref, bool& use_background_color_mode_ref,
                   Vec3& cloud_animation_offset_ref, double& current_time_ref);
    
    // RenderBackend interface implementation
    void renderScene(const Scene& scene, int width, int height, 
                    const Vec3& camera_pos, const Vec3& camera_dir, 
                    const Vec3& camera_up, double fov,
                    std::vector<std::vector<ConsoleCell>>& output_buffer) override;
    
    bool initialize() override;
    void cleanup() override;
    RenderBackendType getType() const override { return RenderBackendType::SOFTWARE; }
    bool isAvailable() const override { return true; } // Software backend is always available
    
private:
    // References to renderer state (shared with main Renderer class)
    bool& show_normals_mode;
    bool& use_background_color_mode;
    Vec3& cloud_animation_offset;
    double& current_time;
    
    // Software raytracing implementation
    ConsoleCell castRay(const Ray& ray, const Scene& scene);
    
    // Multi-threading support for software rendering
    struct RenderTask {
        std::future<std::vector<std::vector<ConsoleCell>>> future;
        bool active = false;
    };
    
    static constexpr int MAX_RENDER_THREADS = 8;
    std::vector<RenderTask> render_tasks;
};

#endif // SOFTWARE_BACKEND_H
